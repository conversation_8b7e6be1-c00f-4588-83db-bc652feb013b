SR Research PyLink
Version: 2.1.1197.0

Generated: September 27, 2024

Generated by Doxygen 1.8.17






i

1 Introduction 1
1.1 Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1

1.1.1 Organization of This Document . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1

1.1.2 Getting Started . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1

1.2 EyeLink Programming Conventions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1

1.2.1 Outline of a Typical Windows Experiment . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2

1.2.2 Standard Messages . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3

1.3 Overview of PyLink module . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3

2 Module Index 5
2.1 Modules . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 5

3 Module Documentation 7
3.1 Tracker Data Type Constants . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7

3.1.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7

3.2 Event Type Flags . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 8

3.2.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 8

3.3 Event Data Flags . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 9

3.3.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 9

3.4 Special Key values for the tracker . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 10

3.4.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 10

3.5 Tracker Mode values . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 11

3.5.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 11

3.6 EyeLink Graphics Functions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12

3.6.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12

3.6.2 Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12

******* openGraphicsEx() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12

******* openGraphics() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12

******* setCalibrationColors() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 13

******* setTargetSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 14

******* setCalibrationSounds() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 14

******* setDriftCorrectSounds() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 15

******* setCameraPosition() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 15

******* getDisplayInformation() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 16

******* closeGraphics() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 16

3.7 EyeLink Utility Functions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17

3.7.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17

3.7.2 Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17

******* currentDoubleUsec() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17

******* inRealTimeMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 18

******* endRealTimeMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 18

******* flushGetkeyQueue() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 18

******* getLastError() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19

© SR Research Ltd. 2003-2024



ii

******* bitmapSave() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19

******* getDisplayAPIVersion() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20

******* closeMessageFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20

3.7.2.9 currentUsec() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20

*******0 alert() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20

*******1 beginRealTimeMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 21

*******2 currentTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 21

*******3 msecDelay() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 21

*******4 openMessageFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22

*******5 pumpDelay() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22

*******6 getEYELINK() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22

4 Class Documentation 25

4.1 ButtonEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25

4.1.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25

4.1.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25

******* getButtons() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25

******* getStates() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26

4.2 DisplayInfo Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26

4.2.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26

4.2.2 Member Data Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26

******* width . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 26

******* height . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

******* bits . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

******* refresh . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

4.3 EndBlinkEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

4.3.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

4.3.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 27

******* getEndTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28

4.4 EndFixationEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28

4.4.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28

4.4.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28

******* getAverageGaze() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28

******* getAverageHREF() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 29

******* getAveragePupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 29

******* getEndPupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 29

4.5 EndNonBlinkEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 29

4.5.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 30

4.5.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 30

******* getEndTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 31

******* getEndGaze() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 31

******* getEndHREF() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 31

© SR Research Ltd. 2003-2024



iii

******* getEndVelocity() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 31

******* getAverageVelocity() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 32

******* getPeakVelocity() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 32

******* getEndPPD() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 32

4.6 EndSaccadeEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 32

4.6.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 33

4.6.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 33

******* getAmplitude() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 33

******* getAngle() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 33

4.7 EyeEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 33

4.7.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 34

4.7.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35

******* getTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35

******* getType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35

******* getEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35

******* getRead() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 36

******* getStartTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 36

4.8 EyeLink Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 36

4.8.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 38

4.8.2 Constructor & Destructor Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39

******* __init__() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39

4.8.3 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39

******* progressUpdate() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39

******* progressSendDataUpdate() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39

******* setSampleSizeForVelAndAcceleration() . . . . . . . . . . . . . . . . . . . . . . . 40

******* setVelocityAccelerationModel() . . . . . . . . . . . . . . . . . . . . . . . . . . . . 40

******* doTrackerSetup() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 40

******* setAcceptTargetFixationButton() . . . . . . . . . . . . . . . . . . . . . . . . . . . 41

******* setCalibrationType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 41

4.8.3.8 setXGazeConstraint() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 42

4.8.3.9 setYGazeConstraint() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 42

*******0 enableAutoCalibration() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 43

*******1 disableAutoCalibration() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 43

*******2 setAutoCalibrationPacing() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 43

*******3 readIOPort() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 44

*******4 writeIOPort() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 44

*******5 setHeuristicLinkAndFileFilter() . . . . . . . . . . . . . . . . . . . . . . . . . . . 44

*******6 setHeuristicFilterOn() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 45

*******7 setHeuristicFilterOff() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 45

*******8 setPupilSizeDiameter() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 46

*******9 setSimulationMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 46

*******0 setScreenSimulationDistance() . . . . . . . . . . . . . . . . . . . . . . . . . . . 46

© SR Research Ltd. 2003-2024



iv

*******1 markPlayBackStart() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 47

*******2 setNoRecordEvents() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 47

*******3 setFileSampleFilter() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 48

*******4 setFileEventData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 48

*******5 setFileEventFilter() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49

*******6 setLinkSampleFilter() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49

*******7 setLinkEventData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 50

*******8 setLinkEventFilter() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 51

*******9 setSaccadeVelocityThreshold() . . . . . . . . . . . . . . . . . . . . . . . . . . . 52

*******0 setAccelerationThreshold() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 53

*******1 setMotionThreshold() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 53

*******2 setPursuitFixup() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 54

*******3 setUpdateInterval() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 54

*******4 setFixationUpdateAccumulate() [1/2] . . . . . . . . . . . . . . . . . . . . . . . 54

*******5 setRecordingParseType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 55

*******6 drawText() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 55

*******7 clearScreen() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 56

*******8 drawLine() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 56

*******9 drawBox() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 56

*******0 drawFilledBox() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 57

*******1 getFixationUpdateInterval() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 58

*******2 getFixationUpdateAccumulate() . . . . . . . . . . . . . . . . . . . . . . . . . . . 58

*******3 setFixationUpdateInterval() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 58

*******4 setFixationUpdateAccumulate() [2/2] . . . . . . . . . . . . . . . . . . . . . . . 58

*******5 echo() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 58

*******6 drawCross() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 59

4.9 EyeLinkAddress Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 59

4.9.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 60

4.9.2 Constructor & Destructor Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . 60

******* __init__() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 60

4.9.3 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 60

******* getIP() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 60

******* getPort() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 61

4.10 EyeLinkCBind Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 61

4.10.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 63

4.10.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 63

******** calculateOverallVelocityAndAcceleration() . . . . . . . . . . . . . . . . . . . . . 63

******** getTrackerVersion() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 64

******** nodeSendMessage() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 64

******** getkey() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 65

******** trackerTimeUsecOffset() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 65

******** startPlayBack() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 66

© SR Research Ltd. 2003-2024



v

******** doTrackerSetup() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 66

******** inSetup() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 66

4.10.2.9 stopData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 67

********0 receiveDataFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 67

********1 readKeyQueue() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 67

********2 sendCommand() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 68

********3 reset() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 68

********4 openDataFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 69

********5 sendTimedCommandEx() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 69

********6 eyeAvailable() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 70

********7 userMenuSelection() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 70

********8 dummy_open() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 70

********9 calculateVelocityXY() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 70

********0 imageModeDisplay() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 71

********1 requestTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 72

********2 calculateVelocity() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 72

********3 waitForBlockStart() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 72

********4 echo_key() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 73

********5 sendMessage() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 73

********6 getNode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 74

********7 readKeyButton() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 74

********8 bitmapBackdrop() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 75

********9 pollTrackers() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 75

********0 close() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 76

********1 key_message_pump() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 76

********2 closeDataFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 76

********3 getNextData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 76

********4 acceptTrigger() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 77

********5 startRecording() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 77

********6 getLastMessage() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 77

********7 readReply() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 78

********8 trackerTimeUsec() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 78

********* getEventDataFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 78

********0 dataSwitch() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 79

********1 isRecording() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 79

********* readRequest() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 80

********* getTrackerMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 80

********* sendKeybutton() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 81

********5 startSetup() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 81

********6 quietMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 82

********7 getModeData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 82

********* nodeSend() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 83

© SR Research Ltd. 2003-2024



vi

********* terminalBreak() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 83

********* startData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 84

********* pollResponses() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 84

********* getLastButtonStates() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 85

********3 isConnected() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 85

********4 waitForData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 85

********5 broadcastOpen() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 86

********* getRecordingStatus() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 86

********* open() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 87

********* targetModeDisplay() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 87

********9 getCalibrationResult() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 87

********0 nodeReceive() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 88

********1 getDataCount() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 88

********2 getLastData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 88

********3 getSample() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 89

********4 doDriftCorrect() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 89

********5 setOfflineMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 90

********6 getNewestSample() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 90

********7 breakPressed() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 90

********8 setName() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 90

********9 getCurrentMode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 91

********0 resetData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 91

********1 getCalibrationMessage() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 91

********2 pollRemotes() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 92

********3 sendDataFile() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 92

********4 getTrackerVersionString() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 93

********5 escapePressed() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 93

********6 pumpMessages() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 94

********7 getSampleDataFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 94

********8 stopRecording() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 94

********9 getButtonStates() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 95

********0 abort() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 95

********1 stopPlayBack() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 96

********2 sendTimedCommand() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 96

********* getLastButtonPress() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 97

********* getkeyEx() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 97

********* isInDataBlock() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 98

********* nodeRequestTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 98

********* applyDriftCorrect() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 99

********* setAddress() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 99

********9 readTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 99

********* getImageCrossHairData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 100

© SR Research Ltd. 2003-2024



vii

********* bitmapSaveAndBackdrop() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 100

********* getEventTypeFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 101

********* trackerTimeOffset() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 101

********* getPositionScalar() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 102

********* waitForModeReady() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 102

********* exitCalibration() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 102

********* openNode() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 103

********* flushKeybuttons() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 103

********* commandResult() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 103

********00 getFloatData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 104

********01 getTargetPositionAndState() . . . . . . . . . . . . . . . . . . . . . . . . . . . 105

********02 startDriftCorrect() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 105

********03 trackerTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 105

4.11 EyeLinkCustomDisplay Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 106

4.11.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 107

4.11.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 107

******** __updateimgsize__() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 107

******** setup_cal_display() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 108

******** exit_cal_display() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 108

******** record_abort_hide() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 108

******** setup_image_display() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 108

******** image_title() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 109

******** draw_image_line() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 109

******** set_image_palette() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 109

******** erase_cal_target() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 110

********0 draw_cal_target() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 110

********1 play_beep() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 110

********2 get_input_key() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 111

********3 draw_line() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 111

********4 draw_lozenge() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 112

********5 get_mouse_state() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 112

********6 draw_cross_hair() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 113

4.12 EyeLinkListener Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 113

4.12.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 113

4.12.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 114

******** getTrackerInfo() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 114

******** drawCalTarget() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 114

******** sendMessage() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 114

******** imageBackdrop() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 115

4.13 EyelinkMessage Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 116

4.13.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 116

4.13.2 Constructor & Destructor Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . 116

© SR Research Ltd. 2003-2024



viii

******** __init__() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 116

4.13.3 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 117

******** getText() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 117

4.14 FixUpdateEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 117

4.14.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 117

4.14.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 118

******** getStartPupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 118

******** getAverageGaze() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 118

******** getAverageHREF() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 118

******** getAveragePupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 119

******** getEndPupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 119

4.15 ILinkData Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 119

4.15.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 121

4.15.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 121

******** getTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getSampleRate() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getSampleDivisor() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getPrescaler() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getVelocityPrescaler() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getPupilPrescaler() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 122

******** getHeadDistancePrescaler() . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

******** getSampleDataFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

******** getEventDataFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

********0 getEventTypeFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

********1 isInBlockWithSamples() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

********2 isInBlockWithEvents() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 123

********3 haveLeftEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********4 haveRightEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********5 getLostDataTypes() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********6 getLastBufferType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********7 getLastBufferSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********8 isControlEvent() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 124

********9 isNewBlock() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********0 getLastItemTimeStamp() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********1 getLastItemType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********2 getLastItemContent() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********3 getBlockNumber() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********4 getSamplesInBlock() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 125

********5 getEventsInBlock() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

********6 getLastResX() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

********7 getLastResY() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

********8 getLastPupil() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

© SR Research Ltd. 2003-2024



ix

********9 getLastItemStatus() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

********0 getSampleQueueLength() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 126

********1 getEventQueueLength() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********2 getQueueSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********3 getFreeQueueLength() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********4 getLastReceiveTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********5 isSamplesEnabled() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********6 isEventsEnabled() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 127

********7 getPacketFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********8 getLinkFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********9 getStateFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********0 getTrackerDataOutputState() . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********1 getPendingCommands() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********2 isPoolingRemote() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 128

********3 getPoolResponse() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********4 getReserved() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********5 getName() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********6 getTrackerName() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********7 getNodes() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********8 getLastItem() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 129

********9 getAddress() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 130

********0 getTrackerAddress() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 130

********1 getTrackerBroadcastAddress() . . . . . . . . . . . . . . . . . . . . . . . . . . . 130

4.16 IOEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 130

4.16.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 131

4.16.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 131

******** getTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 131

******** getType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 131

******** getData() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 132

4.17 KeyInput Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 132

4.17.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 132

4.18 MessageEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 132

4.18.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 132

4.18.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 133

******** getTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 133

******** getType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 133

******** getText() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 133

4.19 Sample Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 133

4.19.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 134

4.19.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 135

******** isLeftSample() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 135

******** isRightSample() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 135

© SR Research Ltd. 2003-2024



x

******** isBinocular() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 135
******** getTime() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 136
******** getType() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 136
******** getPPD() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 136
******** getStatus() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 136
******** getInput() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 137
4.19.2.9 getFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 137
********0 getButtons() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 137
********1 getRightEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 137
********2 getLeftEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 138
********3 getEye() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 138
********4 getTargetDistance() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 138
********5 getTargetX() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 138
********6 getTargetY() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 139
********7 getTargetFlags() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 139

4.20 SampleData Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 139
4.20.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 140
4.20.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 140

******** getGaze() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 140
******** getHREF() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 140
******** getRawPupil() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 141
******** getPupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 141

4.21 StartBlinkEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 141
4.21.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 141

4.22 StartFixationEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 141
4.22.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 142
4.22.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 142

******** getStartPupilSize() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 142
4.23 StartNonBlinkEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 142

4.23.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 143
4.23.2 Member Function Documentation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 143

******** getStartGaze() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 143
******** getStartHREF() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 143
******** getStartVelocity() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 143
******** getStartPPD() . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 144

4.24 StartSaccadeEvent Class Reference . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 144
4.24.1 Detailed Description . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 144

5 Example implementation of EyeLinkCustomDisplay 145

Index 151

© SR Research Ltd. 2003-2024



Chapter 1

Introduction

1.1 Introduction

Performing research with eye-tracking equipment typically requires carefully implemented software tools to collect,
process, and analyze data. Much of these tools involve tracker calibration, real-time data collection, and so on.

The EyeLink® eye-tracking system is most powerful when used with the Ethernet link interface, which allows flexible
control of data collection and real-time data transfer. The PyLink module implements all core EyeLink functions and
classes for EyeLink connection and the EyeLink graphics, such as the display of the camera image, calibration,
validation, and drift-check. The EyeLink graphics included in the EyeLink Developer's Kit are currently implemented
using the SDL library (www.libsdl.org). However, users can implement custom EyeLink graphics with a library they
prefer to use for stimulus presentation (see the PsychoPy and Pygame examples bundled in the EyeLink Developer's
Kit).

1.1.1 Organization of This Document

EyeLink Programming Conventions of this document introduces the standard programming convention recom-
mended for an EyeLink experiment, and the standard EyeLink messages that can be used to facilitate data analysis
and visualization in Data Viewer. Overview of PyLink module documents the common functions and classes used
in the PyLink module in detail. You will rarely need other functions or classes than those listed here.

1.1.2 Getting Started

Please refer to the EyeLink Installation Guide ( https://www.sr-support.com/forum-34.html) for
instructions on how to set up the stimulus presentation PC (hereby referred to as the "Display PC"). The PyLink
library also comes with a Getting Started Guide for Python and PyLink, which clearly explains the installation of
Python and PyLink, the example scripts provided by us, the typical structure of an EyeLink experiment, and some
troubleshooting tips. For general knowledge about Python programming, you may refer to the tutorials available on
the official Python website, https://www.python.org/.

1.2 EyeLink Programming Conventions

The PyLink library contains a set of classes and functions, which are used to program experiments on many different
platforms, such as Windows, Linux (e.g., Ubuntu), and macOS. This chapter will outline the programming convention
of a typical EyeLink experiment, and the standard messages that would allow seamless integration with analysis
tools such as EDF2ASC converter or EyeLink Data Viewers.



2 Introduction

1.2.1 Outline of a Typical Windows Experiment

To help programmers understand how to write experiment software, and how to port existing experiments to the
EyeLink platform, this section outlines the standard operations involved in an EyeLink experiment. A typical experi-
ment using the EyeLink eye-tracker involves some variation of the following sequence of operations:

• Open a link connection to the EyeLink Host PC.

• Set up an EDF file name, and open an EDF data file on the EyeLink Host PC.

• Send configuration commands to the EyeLink Host PC to prepare it for the experiment.

• Open a full-screen window and configure the calibration graphics routines.

• Record one or more blocks of trials. Each block typically begins with tracker setup (camera setup and cali-
bration), and then several trials are run.

• Close the EDF data file. If desired, retrieve the file via the link to the Host PC.

• Close the link connection to the Host PC and terminate the task.

For each trial, the experiment will do these steps:

• Send a message ("TRIALID") to the Host PC to mark the start of a trial.

• Send a record status message to show on the Host PC screen (optional).

• Draw background graphics (image or landmarks) on the Host PC display (optional).

• Perform a drift-check (or drift-correction).

• Start data recording.

• Present experimental stimuli for the trial, and retrieve real-time eye movement data if needed.

• Stop data recording.

• Send a message ("TRIAL_RESULT") to the Host PC to mark the end of a trial.

This sequence of operations is the core of almost all experiments (see the accompanying "Getting Started with
Python and PyLink" doc for an example illustrating the above concept). A real experiment would probably add
practice trials, instruction screens, randomization, and so on.

During recording, all eye-tracking data and events are usually written into the EDF file, which is saved on the eye
tracker's hard disk, and may be copied to the Display PC at the end of the experiment. Your experiment will also
add messages to the EDF file to identify trial conditions and to timestamp important events (such as participant
responses and display changes) for use in analysis. The EDF file may be processed directly using the EyeLink Data
Viewer ( https://www.sr-support.com/forum-7.html), or converted to an ASC file and processed
by your own software.

© SR Research Ltd. 2003-2024



1.3 Overview of PyLink module 3

1.2.2 Standard Messages

Experiments should place certain messages into the EDF file, to mark the start and end of trials. These messages
will facilitate the SR Research viewing and analysis applications (e.g., Data Viewer) to process the EDF files.

Text messages can be sent to the EyeLink Host PC and added to the EDF file along with the eye movement data.
These messages will be time stamped with an accuracy of 1 millisecond from the time sent, and can be used to
mark important events such as display changes. Be careful not to send messages too quickly: the eye tracker can
handle about 20 messages every 10 milliseconds. Above this rate, some messages may be lost before being written
to the EDF file.

To facilitate data analysis in EyeLink Data Viewer, special messages can be added to the EDF file. Examples of
these messages include those that specify the overlay image, the interest areas, and the trial variables. Detailed
information about the various standard messages that Data Viewer recognizes can be found in the "EyeLink Data
Viewer user manual" ( https://www.sr-support.com/thread-135.html).

• The "TRIALID" message is sent at the beginning of a trial, before the start of data recording. It is not manda-
tory, but it is recommended to include a unique trial identifier in the message, for instance, "TRIALID 13".

• The "TRIAL_RESULT" message is sent after the recording ends. Importantly, the variables that would be
used for analysis (i.e., the "TRIAL_VAR" messages) should be written in the EDF file before the "TRIAL_←↩

RESULT" message. Additional info can be included in the "TRAIL_RESULT" message, e.g., to indicate the
result, for instance "TRIAL_RESULT 0".

• The other critical message that worth noting here is the "DISPLAY_COORDS" message. This message is
used by Data Viewer to figure out the appropriate screen size (in pixels) for visualizing the eye movement
data, e.g., "DISPLAY_COORDS 0 0 1023 767". The four integers in the message mark the left, top, right,
and bottom of the screen.

• One or several "!V TRIAL_VAR" messages should be sent to report the experiment condition(s) of a recording
trial. These messages should generally be sent at the end of each trial, allowing variables that may get
updated during the recording, such as response time, accuracy, etc., to be included. These messages should
be sent before the TRIAL_RESULT message, which marks the end of the trial.

• One of several "!V IAREA" messages can be written into the EDF files to allow Data Viewer to display interest
areas during analysis.

• One or several "!V IMGLOAD" messages can be sent to the EDF file to specify the background images for
fixation/saccade/heatmap data visualization.

• Other Data Viewer integration messages can be used to play back video stimuli, draw positions of target
traces, or draw simple graphics such as boxes, lines, or circles to mark object locations on the screen. Please
see section "Protocol for EyeLink Data to Viewer Integration" of the EyeLink Data Viewer User Manual.

• Users should also send messages to the EDF file to mark the critical events in a trial (e.g., onset/offset
of a critical display, start/end of the audio playing, participant's keyboard/mouse/button responses. These
messages can be used in Data Viewer to create interest period or reaction time definition for data filtering.

1.3 Overview of PyLink module

The interaction of the EyeLink tracker and your experiment is fairly sophisticated: for example, in the Setup menu it
is possible to perform calibration or validation, display a camera image on the Display PC to aid in participant setup,
and record data with the experiment following along by displaying proper graphics. Large files can be transferred
over the link, and should the EyeLink tracker software terminate, the experiment application is automatically termi-
nated as well. Keys pressed on the Display PC keyboard are transferred to the EyeLink PC and operate the tracker
during setup, and buttons pressed on the tracker button box may be used to control the execution of the experiment
on the Display PC.

© SR Research Ltd. 2003-2024



4 Introduction

PyLink, a Python wrapper for the EyeLink core API, implements all of the functions and classes for EyeLink connec-
tion and graphics, such as the display of camera image, calibration, validation, and drift correct. Each of the above
operations required just one line of code in your program. Almost all of the source code you will need to write is for
the experiment itself, e.g., the control of trials, the presentation and generation of visual and auditory stimuli, and
error handling.

Please see a detailed description of all functions in the pyLink module and all classes in this module under Classes
section.

© SR Research Ltd. 2003-2024



Chapter 2

Module Index

2.1 Modules

Here is a list of all modules:

Tracker Data Type Constants . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7
Event Type Flags . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 8
Event Data Flags . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 9
Special Key values for the tracker . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 10
Tracker Mode values . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 11
EyeLink Graphics Functions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12
EyeLink Utility Functions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17



6 Module Index

© SR Research Ltd. 2003-2024



Chapter 3

Module Documentation

3.1 Tracker Data Type Constants

3.1.1 Detailed Description



8 Module Documentation

3.2 Event Type Flags

The following specifies what types of events were written by tracker.

3.2.1 Detailed Description

The following specifies what types of events were written by tracker.

© SR Research Ltd. 2003-2024



3.3 Event Data Flags 9

3.3 Event Data Flags

The following specifies what types of data were included in events by tracker.

3.3.1 Detailed Description

The following specifies what types of data were included in events by tracker.

© SR Research Ltd. 2003-2024



10 Module Documentation

3.4 Special Key values for the tracker

The following specifies special key values for the tracker.

3.4.1 Detailed Description

The following specifies special key values for the tracker.

© SR Research Ltd. 2003-2024



3.5 Tracker Mode values 11

3.5 Tracker Mode values

Set of bit flags that mark mode function:

3.5.1 Detailed Description

Set of bit flags that mark mode function:

© SR Research Ltd. 2003-2024



12 Module Documentation

3.6 EyeLink Graphics Functions

Functions

• def openGraphicsEx (eyeCustomDisplay)
• def openGraphics (∗args)
• def setCalibrationColors (∗args)
• def setTargetSize (∗args)
• def setCalibrationSounds (∗args)
• def setDriftCorrectSounds (∗args)
• def setCameraPosition (∗args)
• def getDisplayInformation (∗args)
• def closeGraphics ()

3.6.1 Detailed Description

3.6.2 Function Documentation

******* openGraphicsEx()

def pylink.openGraphicsEx (
eyeCustomDisplay )

Allow one to configure the graphics with EyeLinkCustomDisplay. See EyeLinkCustomDisplay for more details.
genv = EyeLinkCoreGraphicsPsychoPy(el_tracker, win)
pylink.openGraphicsEx(genv)

Parameters

eyeCustomDisplay instance of EyeLinkCustomDisplay for the desired platform. This cannot be used with
eyelink_core_graphics library or openGraphics

******* openGraphics()

def pylink.openGraphics (
∗ args )

Opens the graphics if the display mode is not set. If the display mode is already set, uses the existing display mode.

Remarks

This is equivalent to the SDL version C API
INT16 init_expt_graphics(SDL_Surface * s, DISPLAYINFO *info);

© SR Research Ltd. 2003-2024



3.6 EyeLink Graphics Functions 13

Parameters

dimension Two-item tuple of display containing width and height information.
bits Color bits.

Returns

None or run-time error.

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

******* setCalibrationColors()

def pylink.setCalibrationColors (
∗ args )

Passes the colors of the display background and fixation target to the eyelink_core_graphics library. During cal-
ibration, camera image display, and drift correction, the display background should match the brightness of the
experimental stimuli as closely as possible, in order to maximize tracking accuracy. This function passes the colors
of the display background and fixation target to the eyelink_core_graphics library. This also prevents flickering of
the display at the beginning and end of drift correction.

Remarks

This is equivalent to the C API
void set_calibration_colors(SDL_Color *fg, SDL_Color *bg);

Parameters

foreground_color Color for foreground calibration target.
background_color Color for foreground calibration background.

Both colors must be a three-integer (from 0 to 255) tuple encoding the red, blue, and green color component.

Example:
setCalibrationColors((0, 0, 0), (255, 255, 255))
This sets the calibration target in black and calibration background in white.

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

© SR Research Ltd. 2003-2024



14 Module Documentation

******* setTargetSize()

def pylink.setTargetSize (
∗ args )

The standard calibration and drift correction target is a disk (for peripheral delectability) with a central "hole" target
(for accurate fixation). The sizes of these features may be set with this function.

Remarks

This function is equivalent to the C API
void set_target_size(UINT16 diameter, UINT16 holesize);

Parameters

diameter Size of outer disk, in pixels.
holesize Size of central feature, in pixels. If holesize is 0, no central feature will be drawn. The disk is drawn

in the calibration foreground color, and the hole is drawn in the calibration background color.

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

******* setCalibrationSounds()

def pylink.setCalibrationSounds (
∗ args )

Selects the sounds to be played during do_tracker_setup(), including calibration, validation and drift correc-
tion. These events are the display or movement of the target, successful conclusion of calibration or good validation,
and failure or interruption of calibration or validation.

Remarks

If no sound card is installed, the sounds are produced as "beeps" from the PC speaker. Otherwise, sounds
can be selected by passing a string. If the string is "" (empty), the default sounds are played. If the string is
"off", no sound will be played for that event. Otherwise, the string should be the name of a .WAV file to play.
This function is equivalent to the C API
void set_cal_sounds(char *target, char *good, char *error);

Parameters

target Sets sound to play when target moves.
good Sets sound to play on successful operation.
error Sets sound to play on failure or interruption.

© SR Research Ltd. 2003-2024



3.6 EyeLink Graphics Functions 15

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

******* setDriftCorrectSounds()

def pylink.setDriftCorrectSounds (
∗ args )

Selects the sounds to be played during doDriftCorrect(). These events are the display or movement of the
target, successful conclusion of drift correction, and pressing the 'ESC' key to start the Setup menu.

Remarks

If no sound card is installed, the sounds are produced as "beeps" from the PC speaker. Otherwise, sounds
can be selected by passing a string. If the string is "" (empty), the default sounds are played. If the string is
"off", no sound will be played for that event. Otherwise, the string should be the name of a .WAV file to play.
This function is equivalent to the C API
void set_dcorr_sounds(char *target, char *good, char *setup);

Parameters

target Sets sound to play when target moves.
good Sets sound to play on successful operation.
setup Sets sound to play on 'ESC' key pressed.

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

******* setCameraPosition()

def pylink.setCameraPosition (
∗ args )

Sets the camera position on the display computer. Moves the top left hand corner of the camera position to new
location.

Parameters

left X coordinate of upper-left corner of the camera image window.
top Y coordinate of upper-left corner of the camera image window.
right X coordinate of lower-right corner of the camera image window.
bottom Y coordinate of lower-right corner of the camera image window.

© SR Research Ltd. 2003-2024



16 Module Documentation

Remarks

This function only works with SDL 1.2 in conjunction with eyelink_core_graphics library and cannot be used
with EyeLinkCustomDisplay

******* getDisplayInformation()

def pylink.getDisplayInformation (
∗ args )

Returns the display configuration.

Returns

Instance of DisplayInfo class. The width, height, bits, and refresh rate of the display can be accessed from the
returned value.

Example:
display = getDisplayInformation()
print display.width, display.height, display.bits, display.refresh

******* closeGraphics()

def pylink.closeGraphics ( )

Notifies the eyelink_core_graphics or the EyeLinkCustomDisplay to close or release the graphics.

© SR Research Ltd. 2003-2024



3.7 EyeLink Utility Functions 17

3.7 EyeLink Utility Functions

Functions

• def currentDoubleUsec ()
• def inRealTimeMode ()
• def endRealTimeMode ()
• def flushGetkeyQueue ()
• def getLastError ()
• def bitmapSave ()
• def getDisplayAPIVersion ()
• def closeMessageFile ()
• def currentUsec ()
• def alert ()
• def beginRealTimeMode ()
• def currentTime ()
• def msecDelay ()
• def openMessageFile ()
• def pumpDelay ()
• def getEYELINK ()

3.7.1 Detailed Description

3.7.2 Function Documentation

******* currentDoubleUsec()

def currentDoubleUsec ( )

Returns the current microsecond time (as a double type) since the initialization of the EyeLink library.

Remarks

Same as currentUsec() except, this function can return large microseconds. That is the
currentUsec() can return up to 2147483648 microseconds starting from initialization. The
currentDoubleUsec() can return up to 36028797018963968 microseconds. This is equivalent to
the C API
double current_double_usec(void);

Returns

A float data for the current microsecond time since the initialization of the EyeLink library.

© SR Research Ltd. 2003-2024



18 Module Documentation

******* inRealTimeMode()

def inRealTimeMode ( )

returns whether the current mode is real-time.

Returns

1 if in realtime mode, else 0.

******* endRealTimeMode()

def endRealTimeMode ( )

Returns the application to a priority slightly above normal, to end realtime mode. This function should execute
rapidly, but there is the possibility that Windows will allow other tasks to run after this call, causing delays of 1-20
milliseconds.

Remarks

This function is equivalent to the C API
void end_realtime_mode(void);

******* flushGetkeyQueue()

def flushGetkeyQueue ( )

Initializes the key queue used by getkey(). It may be called at any time to get rid any of old keys from the queue.

Remarks

This is equivalent to the C API
void flush_getkey_queue(void);

© SR Research Ltd. 2003-2024



3.7 EyeLink Utility Functions 19

******* getLastError()

def getLastError ( )

Retrieves a tuple containing the error number and error message generated by last Pylink call to have failed

Remarks

This has no equivalence in C API. This can be used to help identify the Runtime Error last raised by a Pylink
call

Returns

(Error-Number, Error-Message) tuple or (0,'') if no Runtime Error has been raised.

Example:
try:

getEYELINK().waitForBlockStart(500,1,0)
except RuntimeError:

if getLastError()[0] == 0: # wait time expired without link data
print ("ERROR: No link samples received!")
return TRIAL_ERROR

else: # for any other status simply re-raise the exception
raise

See also

commandResult()

******* bitmapSave()

def bitmapSave ( )

This function saves the entire bitmap or selected part of a bitmap in an image file (with an extension of .png, .bmp,
.jpg, or .tif). It creates the specified file if this file does not exist.

Parameters

iwidth Original image width.
iheight Original image height.
pixels Pixels of the image in one of two possible formats: pixel=[line1, line2, ... linen]

line=[pix1,pix2,...,pixn],pix=(r,g,b). pixel=[line1, line2, ... linen]
line=[pix1,pix2,...,pixn],pix=0xAARRGGBB.

xs Crop x position.
ys Crop y position.
width Crop width.
height Crop height.
fname File name to save.
path Path to save.
svoptions Save options(SV_NOREPLACE, SV_MAKEPATH). If the file exists, it replaces the file unless

SV_NOREPLACE is specified.

© SR Research Ltd. 2003-2024



20 Module Documentation

This function is equivalent to the C API el_bitmap_save()

******* getDisplayAPIVersion()

def getDisplayAPIVersion ( )

Returns text associated with last command response: may have error message.

Remarks

This is equivalent to the C API
INT16 eyelink_dll_version(char *buf);

Returns

Text associated with last command response or None.

******* closeMessageFile()

def closeMessageFile ( )

Flush and close message file, opened by openMessageFile().

3.7.2.9 currentUsec()

def currentUsec ( )

Returns the current microsecond time since the initialization of the EyeLink library.

Remarks

This is equivalent to the C API
UINT32 current_usec(void);

Returns

Long integer for the current microsecond time since the initialization of the EyeLink library.

*******0 alert()

def alert ( )

This method is used to give a notification to the user when an error occurs.

Remarks

This function does not allow printf formatting as in c. However you can do a formatted string argument in
python. This is equivalent to the C API
void alert_printf(char *fmt, ...);

© SR Research Ltd. 2003-2024



3.7 EyeLink Utility Functions 21

Parameters

message Text message to be displayed.

*******1 beginRealTimeMode()

def beginRealTimeMode ( )

Sets the application priority and cleans up pending Windows activity to place the application in realtime mode. This
could take up to 100 milliseconds, depending on the operation system, to set the application priority.

Remarks

This function is equivalent to the C API
void begin_realtime_mode(UINT32 delay);

Parameters

delay An integer, used to set the minimum time this function takes, so that this function can act as a useful delay.

*******2 currentTime()

def currentTime ( )

Returns the current millisecond time since the initialization of the EyeLink library.

Remarks

This function is equivalent to the C API
UINT32 current_time(void);

Returns

Long integer for the current millisecond time since the initialization of the EyeLink library.

*******3 msecDelay()

def msecDelay ( )

Does a unblocked delay using currentTime().

Remarks

This is equivalent to the C API
void msec_delay(UINT32 n);

© SR Research Ltd. 2003-2024



22 Module Documentation

Parameters

delay An integer for number of milliseconds to delay.

*******4 openMessageFile()

def openMessageFile ( )

Creates message file, once open call to sendMessageToFile(), will not send messages to tracker. Messages are
kept in a queue if the application is in realtime mode, and written to disk on non real-time mode except when
closeMessageFile() is called while in real-time mode.

Parameters

in fname Message file name

*******5 pumpDelay()

def pumpDelay ( )

During calls to msecDelay(), Windows is not able to handle messages. One result of this is that windows may
not appear. This is the preferred delay function when accurate timing is not needed. It calls pumpMessages()
until the last 20 milliseconds of the delay, allowing Windows to function properly. In rare cases, the delay may be
longer than expected. It does not process modeless dialog box messages.

Remarks

This is equivalent to the C API
void pump_delay(UINT32 delay);

Parameters

delay An integer, which sets number of milliseconds to delay.

*******6 getEYELINK()

def pylink.getEYELINK ( )

Returns the EyeLink tracker object

The returned instance had been created when calling EyeLink().

© SR Research Ltd. 2003-2024



3.7 EyeLink Utility Functions 23

Remarks

This function replaces the previous convention of using EYELINK. (EYELINK is no longer constant, it is now
initialized with None)

© SR Research Ltd. 2003-2024



24 Module Documentation

© SR Research Ltd. 2003-2024



Chapter 4

Class Documentation

4.1 ButtonEvent Class Reference

A ButtonEvent class, derived from the IOEvent class, is created to handle specifics of button events.

Inherits IOEvent.

Public Member Functions
• def getButtons (self)

A list of buttons pressed or released.
• def getStates (self)

The button state (1 for pressed or 0 for released).

4.1.1 Detailed Description

A ButtonEvent class, derived from the IOEvent class, is created to handle specifics of button events.

For this class, in addition to the generic IOEvent class methods, two additional methods can be used for instances
of the ButtonEvent class.

Returned by getFloatData() whenever there is a Button Event.

4.1.2 Member Function Documentation

******* getButtons()

def getButtons (
self )

A list of buttons pressed or released.

Returns

List of integers.



26 Class Documentation

******* getStates()

def getStates (
self )

The button state (1 for pressed or 0 for released).

Returns

List of integers.

4.2 DisplayInfo Class Reference

The DisplayInfo class is used to contain information on display configurations, including width, height, color bits, and
refresh rate.

Public Attributes

• width
• height
• bits
• refresh

4.2.1 Detailed Description

The DisplayInfo class is used to contain information on display configurations, including width, height, color bits, and
refresh rate.

The current display configuration can be retrieved by the getDisplayInformation() function of the pylink
module.

For example:
from pylink import *
// Code to open the display
currentDisplay = getDisplayInformation();

print("Current display settings: ", currentDisplay.width, currentDisplay.height, \
print "Current display settings: ", currentDisplay.width, currentDisplay.height, \

currentDisplay.bits, currentDisplay.refresh

4.2.2 Member Data Documentation

******* width

width

Display width in screen pixels.

© SR Research Ltd. 2003-2024



4.3 EndBlinkEvent Class Reference 27

******* height

height

Display height in screen pixels.

******* bits

bits

Color resolution, in bits per pixel, of the display device (for example: 4 bits for 16 colors, 8 bits for 256 colors, or 16
bits for 65,536 colors).

******* refresh

refresh

Refresh rate.

4.3 EndBlinkEvent Class Reference

Class to represent End Blink event.

Inherits EyeEvent.

Public Member Functions

• def getEndTime (self)
Timestamp of the last sample of the blink.

4.3.1 Detailed Description

Class to represent End Blink event.

This also contains the Start Blink data. This also inherits all properties from EyeEvent.

4.3.2 Member Function Documentation

© SR Research Ltd. 2003-2024



28 Class Documentation

******* getEndTime()

def getEndTime (
self )

Timestamp of the last sample of the blink.

Returns

Long integer.

4.4 EndFixationEvent Class Reference

Class to represent End Fixation event.

Inherits StartFixationEvent, and EndNonBlinkEvent.

Public Member Functions
• def getAverageGaze (self)

The average gaze position during the fixation period (in pixel coordinates set by the screen_pixel_coords
command).

• def getAverageHREF (self)
Average HEADREF position during the fixation period.

• def getAveragePupilSize (self)
Average pupil size (in arbitrary units, area or diameter as selected) during a fixation.

• def getEndPupilSize (self)
Pupil size (in arbitrary units, area or diameter as selected) at the end of a fixation interval.

4.4.1 Detailed Description

Class to represent End Fixation event.

This also contains the Start Fixation data. This also inherits all properties from StartFixationEvent and
EndNonBlinkEvent.

4.4.2 Member Function Documentation

******* getAverageGaze()

def getAverageGaze (
self )

The average gaze position during the fixation period (in pixel coordinates set by the screen_pixel_coords
command).

Returns

Two-item tuple in the format of (float, float).

© SR Research Ltd. 2003-2024



4.5 EndNonBlinkEvent Class Reference 29

******* getAverageHREF()

def getAverageHREF (
self )

Average HEADREF position during the fixation period.

Returns

Two-item tuple in the format of (float, float).

******* getAveragePupilSize()

def getAveragePupilSize (
self )

Average pupil size (in arbitrary units, area or diameter as selected) during a fixation.

Returns

Float.

******* getEndPupilSize()

def getEndPupilSize (
self )

Pupil size (in arbitrary units, area or diameter as selected) at the end of a fixation interval.

Returns

Float.

4.5 EndNonBlinkEvent Class Reference

This interface is never used as is.

Inherited by EndFixationEvent, EndSaccadeEvent, and FixUpdateEvent.

© SR Research Ltd. 2003-2024



30 Class Documentation

Public Member Functions

• def getEndTime (self)
Timestamp of the last sample of the event.

• def getEndGaze (self)
Gaze position at the end of the event (in pixel coordinates set by the screen_pixel_coords command).

• def getEndHREF (self)
HEADREF position at the end of the event.

• def getEndVelocity (self)
Gaze velocity at the end of the event (in visual degrees per second).

• def getAverageVelocity (self)
Average gaze velocity during event (in visual degrees per second).

• def getPeakVelocity (self)
Peak gaze velocity during event (in visual degrees per second).

• def getEndPPD (self)
Angular resolution at the end of the event (in screen pixels per visual degree).

4.5.1 Detailed Description

This interface is never used as is.

EndFixationEvent, EndSaccadeEvent and FixUpdateEvent types inherit this.

Please note that the getStartTime() and getEndTime() methods of the event class are the timestamps of
the first and last samples in the event. To compute duration, subtract these two values and add 4 msec (even in 500
Hz tracking modes, the internal parser of EyeLink II quantizes event times to 4 milliseconds).

Peak velocity returned by getPeakVelocity() for fixations is usually corrupted by terminal segments of the
preceding and following saccades.

The getStartPPD() and getEndPPD() methods contain the angular resolution at the start and end of the
saccade or fixation. The average of the start and end angular resolution can be used to compute the size of
saccades in degrees. This Python code would compute the true magnitude of a saccade from an ENDSACC event
in the following way:
newEvent = getEYELINK().getFloatData()
if isinstance(newEvent, EndFixationEvent):

Start_Gaze = newEvent.getStartGaze()
Start_PPD = newEvent.getStartPPD()
End_Gaze = newEvent.getEndGaze()
End_PPD = newEvent. getEndPPD()

dx = (End_Gaze[0] - Start_Gaze[0]) / ((End_PPD[0] + Start_PPD[0])/2.0);
dy = (End_Gaze[1] - Start_Gaze[1]) / ((End_PPD[1] + Start_PPD[1])/2.0);
dist = math.sqrt(dx*dx + dy*dy);

Please note that the average velocity for saccades may be larger than the saccade magnitude divided by its duration
because of overshoots and returns.

4.5.2 Member Function Documentation

© SR Research Ltd. 2003-2024



4.5 EndNonBlinkEvent Class Reference 31

******* getEndTime()

def getEndTime (
self )

Timestamp of the last sample of the event.

Returns

Long integer.

******* getEndGaze()

def getEndGaze (
self )

Gaze position at the end of the event (in pixel coordinates set by the screen_pixel_coords command).

The first and second items of the returned tuple store the x- and y- gaze position respectively.

Returns

Two-item tuple in the format of (float, float).

******* getEndHREF()

def getEndHREF (
self )

HEADREF position at the end of the event.

The first and second items of the returned tuple store the x- and y- coordinate HREF data respectively.

Returns

Two-item tuple in the format of (float, float).

******* getEndVelocity()

def getEndVelocity (
self )

Gaze velocity at the end of the event (in visual degrees per second).

Returns

Float.

© SR Research Ltd. 2003-2024



32 Class Documentation

******* getAverageVelocity()

def getAverageVelocity (
self )

Average gaze velocity during event (in visual degrees per second).

Returns

Float.

******* getPeakVelocity()

def getPeakVelocity (
self )

Peak gaze velocity during event (in visual degrees per second).

Returns

Float.

******* getEndPPD()

def getEndPPD (
self )

Angular resolution at the end of the event (in screen pixels per visual degree).

Returns

Two-item tuple in the format of (float, float).

4.6 EndSaccadeEvent Class Reference

Class to represent End Saccade event.

Inherits StartSaccadeEvent, and EndNonBlinkEvent.

© SR Research Ltd. 2003-2024



4.7 EyeEvent Class Reference 33

Public Member Functions

• def getAmplitude (self)
Returns the amplitude between the start and end of the event.

• def getAngle (self)
Returns the angle between the start and end of the event.

4.6.1 Detailed Description

Class to represent End Saccade event.

This also contains the Start Saccade data. This also inherits all properties from StartSaccadeEvent and
EndNonBlinkEvent.

4.6.2 Member Function Documentation

******* getAmplitude()

def getAmplitude (
self )

Returns the amplitude between the start and end of the event.

Returns

Amplitude between the start and end of the event.

******* getAngle()

def getAngle (
self )

Returns the angle between the start and end of the event.

Returns

Angle between the start and end of the event.

4.7 EyeEvent Class Reference

The EyeLink tracker simplifies data analysis by detecting important changes in the sample data and placing corre-
sponding events into the data stream.

Inherited by EndBlinkEvent, StartBlinkEvent, and StartNonBlinkEvent.

© SR Research Ltd. 2003-2024



34 Class Documentation

Public Member Functions

• def getTime (self)
Timestamp of the sample causing event (in milliseconds since EyeLink tracker was activated).

• def getType (self)
The event code.

• def getEye (self)
Which eye produced the event: 0 (LEFT_EYE) or 1 (RIGHT_EYE).

• def getRead (self)
Bits indicating which data fields contain valid data (see eye_data.h file for details of the bits information).

• def getStartTime (self)
Timestamp of the first sample of the event.

• def getStatus (self)
Errors and warnings of the event.

4.7.1 Detailed Description

The EyeLink tracker simplifies data analysis by detecting important changes in the sample data and placing corre-
sponding events into the data stream.

These include eye-data events (blinks, saccades, and fixations), button events, input-port events, and messages.
Several classes have been created to holds eye event data (start/end of fixation, start/end of saccade, start/end
of blink, fixation update) information. Start events contain only the start time, and optionally the start eye or gaze
position.
End events contain the start and end time, plus summary data on saccades and fixations.

It is important to remember that data sent over the link does not arrive in strict time sequence.
Typically, eye events (such as STARTSACC and ENDFIX) arrive up to 32 milliseconds after the corresponding
samples, and messages and buttons may arrive before a sample with the same time code. This differs from
the order seen in an ASC file, where the events and samples have been sorted into a consistent order by their
timestamps.

The LOST_DATA_EVENT is a new event, introduced for EyeLink tracker version 2.1 and later, and produced within
the DLL to mark the location of lost data. It is possible that data may be lost, either during recording with real-time
data enabled, or during playback. This might happen because of a lost link packet or because data was not read
fast enough (data is stored in a large queue that can hold 2 to 10 seconds of data, and once it is full the oldest data
is discarded to make room for new data). This event has no data or time associated with it.

Event data returned by the getFloatData() method of the EyeLink class.

For example,
newEvent = getEYELINK().getFloatData()

Right now, the EyeLink Core implements the following eye events:

Constant Name Value Description
STARTBLINK 3 Pupil disappeared (time only)
ENDBLINK 4 Pupil reappeared (duration data)
STARTSACC 5 Start of saccade (with time only)
ENDSACC 6 End of saccade (with summary data)
STARTFIX 7 Start of fixation (with time only)
ENDFIX 8 End of fixation (with summary data)
FIXUPDATE 9 Update within fixation (summary data for interval)
MESSAGEEVENT 24 User-definable text (IMESSAGE structure)
BUTTONEVENT 25 Button state change (IOEVENT structure) © SR Research Ltd. 2003-2024

INPUTEVENT 28 Change of input port (IOEVENT structure)
SAMPLE_TYPE 200 Event flags gap in data stream



4.7 EyeEvent Class Reference 35

Please note that due to the tracker configuration, some of the property information returned may be a missing value
MISSING_DATA (or 0, depending on the field). So make sure you check for the validity of the data before trying
to use them. To do the tracker configuration, the user can use the setLinkEventFilter() and setLink←↩

EventData() methods of the EyeLink class to send commands at the start of the experiment or modify the
DATA.INI file on the tracker PC.

When both eyes are being tracked, left and right eye events are produced. The eye from which data was produced
can be retrieved by the getEye() method.

4.7.2 Member Function Documentation

******* getTime()

def getTime (
self )

Timestamp of the sample causing event (in milliseconds since EyeLink tracker was activated).

Returns

Long integer.

******* getType()

def getType (
self )

The event code.

Returns

Integer.

******* getEye()

def getEye (
self )

Which eye produced the event: 0 (LEFT_EYE) or 1 (RIGHT_EYE).

Returns

Integer.

© SR Research Ltd. 2003-2024



36 Class Documentation

******* getRead()

def getRead (
self )

Bits indicating which data fields contain valid data (see eye_data.h file for details of the bits information).

Returns

Integer.

******* getStartTime()

def getStartTime (
self )

Timestamp of the first sample of the event.

Returns

Long integer.

4.8 EyeLink Class Reference

The EyeLink class is an extension of the EyeLinkListener class with additional utility functions.

Inherits EyeLinkListener.

Public Member Functions

• def __init__ (self, trackeraddress="*********")
Constructor.

• def progressUpdate (self, size, received)
prints out the file transfer progress to the console.

• def progressSendDataUpdate (self, size, sent)
prints out the sending file progress to the console.

• def setSampleSizeForVelAndAcceleration (self, sm)
Sets the sample model to be used for velocity and acceleration calculation.

• def getSampleSizeForVelAndAcceleration (self)
Returns the sample model used for velocity and acceleration calculation.

• def setVelocityAccelerationModel (self, sm)
Sets the sample model to be used for velocity and acceleration calculation.

• def getVelocityAccelerationModel (self)
Returns the sample model used for velocity and acceleration calculation in text form.

• def getTrackerAddress (self)
Returns the tracker address.

• def getDummyMode (self)

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 37

Returns whether in dummy mode or not.
• def doTrackerSetup (self, width=None, height=None)

Switches the EyeLink tracker to the Setup menu, from which camera setup, calibration, validation, drift correction, and
configuration may be performed.

• def setAcceptTargetFixationButton (self, button)
This programs a specific button for use in drift correction.

• def setCalibrationType (self, type)
This command sets the calibration type, and recomputed the calibration targets after a display resolution change.

• def setXGazeConstraint (self, x_position="AUTO")
Locks the x part of gaze position data.

• def setYGazeConstraint (self, y_position="AUTO")
Locks the y part of gaze position data.

• def enableAutoCalibration (self)
Enables the auto calibration mechanism.

• def disableAutoCalibration (self)
Disables the auto calibration mechanism.

• def setAutoCalibrationPacing (self, pace)
Sets automatic calibration pacing.

• def readIOPort (self, ioport)
Sends a command to the tracker to read the specified io port.

• def writeIOPort (self, ioport, data)
Sends a command to the tracker to write the specified io port.

• def setHeuristicLinkAndFileFilter (self, linkfilter, filefilter=-1)
EyeLink II only: Can be used to set level of filtering on the link and analog output, and on file data.

• def setHeuristicFilterOn (self)
EyeLink 1 Only: Can be used to enable filtering, increases system delay by 4 msec if the filter was originally off.

• def setHeuristicFilterOff (self)
EyeLink 1 Only: Can be used to disable filtering, reduces system delay by 4 msec.

• def setPupilSizeDiameter (self, value)
Can be used to determine pupil size information to be recorded.

• def setSimulationMode (self, value)
Can be used to turn off head tracking if not used.

• def setScreenSimulationDistance (self, distance)
Used to compute correct visual angles and velocities when head tracking not used.

• def markPlayBackStart (self)
Marks the location in the data file from which playback will begin at the next call to EYEYLINK.startPlay←↩

Back().
• def setNoRecordEvents (self, message=False, button=False, inputevent=False)

Selects what types of events can be sent over the link while not recording (e.g between trials).
• def setFileSampleFilter (self, list)

Sets data in samples written to EDF file.
• def setFileEventData (self, list)

Sets data in events written to EDF file.
• def setFileEventFilter (self, list)

Sets which types of events will be written to EDF file.
• def setLinkSampleFilter (self, list)

Sets data in samples sent through link.
• def setLinkEventData (self, list)

Sets data in events sent through link.
• def setLinkEventFilter (self, list)

Sets which types of events will be sent through link.

© SR Research Ltd. 2003-2024



38 Class Documentation

• def setSaccadeVelocityThreshold (self, vel)
Sets velocity threshold of saccade detector: usually 30 for cognitive research, 22 for pursuit and neurological work.

• def setAccelerationThreshold (self, accel)
Sets acceleration threshold of saccade detector: usually 9500 for cognitive research, 5000 for pursuit and neuro-
logical work.

• def setMotionThreshold (self, deg)
Sets a spatial threshold to shorten saccades.

• def setPursuitFixup (self, maxvel)
Sets the maximum pursuit velocity accommodation by the saccade detector.

• def setUpdateInterval (self, time)
Normally set to 0 to disable fixation update events.

• def setFixationUpdateAccumulate (self, time)
Normally set to 0 to disable fixation update events.

• def setRecordingParseType (self, rtype="GAZE")
Sets how velocity information for saccade detection is computed.

• def drawText (self, text, pos=(-1,-1))
Draws text, coordinates are gaze-position display coordinates.

• def clearScreen (self, color)
Clear tracker screen for drawing background graphics or messages.

• def drawLine (self, firstPoint, secondPoint, color)
Draws line, coordinates are gaze-position display coordinates.

• def drawBox (self, x, y, width, height, color)
Draws an empty box, coordinates are gaze-position display coordinates.

• def drawFilledBox (self, x, y, width, height, color)
Draws a solid block of color, coordinates are gaze-position display coordinates.

• def getFixationUpdateInterval (self)
Returns the fixation update interval value.

• def getFixationUpdateAccumulate (self)
Returns the fixation update accumulate value.

• def setFixationUpdateInterval (self, interval)
Sends a command to the tracker to update the FixationUpdateInterval.

• def setFixationUpdateAccumulate (self, accumulate)
Sends a command to the tracker to update the FixationUpdateAccumulate.

• def echo (self, text, pos=(-1,-1))
Prints text at current print position to tracker screen, gray on black only.

• def drawCross (self, x, y, color)
Draws a small "+" to mark a target point.

4.8.1 Detailed Description

The EyeLink class is an extension of the EyeLinkListener class with additional utility functions.

Most of these functions are used to perform tracker setups (For current information on the EyeLink tracker config-
uration, examine the ∗.INI files in the EYELINK2\EXE\ or ELCL\EXE\ directory of the eye tracker computer). An
instance of the EyeLink class can be created by using the class constructor function. For example,
try:

EYELINK = EyeLink()
except:

EYELINK = None

An instance of EyeLink class can directly use all of the EyeLinkListener methods. In addition, it has its own methods
as listed in the following. All of the methods should be called in the format of: EYELINK.functionName(parameters),
where EYELINK is an instance of EyeLink class.

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 39

4.8.2 Constructor & Destructor Documentation

******* __init__()

def __init__ (
self,
trackeraddress = "*********" )

Constructor.

Parameters

trackeraddress optional tracker address. If no parameters passed in, default address of ********* is used. If
None is passed as the address, the connection is opened in dummy mode.

4.8.3 Member Function Documentation

******* progressUpdate()

def progressUpdate (
self,
size,
received )

prints out the file transfer progress to the console.

This is called after the call to receiveDataFile

Parameters

size Size of the file.
received Size received so far.

******* progressSendDataUpdate()

def progressSendDataUpdate (
self,
size,
sent )

prints out the sending file progress to the console.

This is called after the call to sendDataFile

© SR Research Ltd. 2003-2024



40 Class Documentation

Parameters

size Size of the file.
sent Size sent so far.

******* setSampleSizeForVelAndAcceleration()

def setSampleSizeForVelAndAcceleration (
self,
sm )

Sets the sample model to be used for velocity and acceleration calculation.

Parameters

sm sample model to be used. Valid values are FIVE_SAMPLE_MODEL,
NINE_SAMPLE_MODEL,SEVENTEEN_SAMPLE_MODEL, and EL1000_TRACKER_MODEL

******* setVelocityAccelerationModel()

def setVelocityAccelerationModel (
self,
sm )

Sets the sample model to be used for velocity and acceleration calculation.

Same as setSampleSizeForVelAndAcceleration excepts this interprets string message.

Parameters

sm sample model to be used. Valid values are 5-sample Model, 9-sample Model,17-sample
Model, and EL1000 Tracker Model

******* doTrackerSetup()

def doTrackerSetup (
self,
width = None,
height = None )

Switches the EyeLink tracker to the Setup menu, from which camera setup, calibration, validation, drift correction,
and configuration may be performed.

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 41

Pressing the 'Esc' key on the tracker keyboard will exit the Setup menu and return from this function. Calling
exitCalibration() from an event handler will cause any call to do_tracker_setup() in progress to
return immediately.

Parameters

width Width of the screen.
height Height of he screen.

******* setAcceptTargetFixationButton()

def setAcceptTargetFixationButton (
self,
button )

This programs a specific button for use in drift correction.

Remarks

This function is equivalent to
getEYELINK().sendCommand("button_function %d ’accept_target_fixation’"%button);

Parameters

button Id of the button that is used to accept target fixation.

******* setCalibrationType()

def setCalibrationType (
self,
type )

This command sets the calibration type, and recomputed the calibration targets after a display resolution change.

Remarks

This function is equivalent to
getEYELINK().sendCommand("calibration_type=%s"%caltype);

© SR Research Ltd. 2003-2024



42 Class Documentation

Parameters

type One of these calibration type codes listed below:
H3 horizontal 3-point calibration
HV3 3-point calibration, poor linearization
HV5 5-point calibration, poor at corners
HV9 9-point grid calibration, best overall
HV13 13-point calibration for large calibration region

(EyeLink II version 2.0 or later; EyeLink 1000)

4.8.3.8 setXGazeConstraint()

def setXGazeConstraint (
self,
x_position = "AUTO" )

Locks the x part of gaze position data.

Usually set to AUTO: this will use the last drift-correction target position when in H3 mode.

Remarks

This function is equivalent to
getEYELINK().sendCommand("x_gaze_constraint=%s"%(str(value)));

Parameters

x_position x gaze coordinate, or AUTO.

4.8.3.9 setYGazeConstraint()

def setYGazeConstraint (
self,
y_position = "AUTO" )

Locks the y part of gaze position data.

Usually set to AUTO: this will use the last drift-correction target position when in H3 mode.

Remarks

This function is equivalent to
getEYELINK().sendCommand("y_gaze_constraint=%s"%(str(value)));

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 43

Parameters

y_position y gaze coordinate, or AUTO.

*******0 enableAutoCalibration()

def enableAutoCalibration (
self )

Enables the auto calibration mechanism.

By default, this mechanism is turned off.

Remarks

This function is equivalent to
if(getEYELINK().isConnected()):

getEYELINK().sendCommand("enable_automatic_calibration=YES")

*******1 disableAutoCalibration()

def disableAutoCalibration (
self )

Disables the auto calibration mechanism.

By default, this mechanism is turned off.

Remarks

This function is equivalent to
if(getEYELINK().isConnected()):

getEYELINK().sendCommand("enable_automatic_calibration=NO")

*******2 setAutoCalibrationPacing()

def setAutoCalibrationPacing (
self,
pace )

Sets automatic calibration pacing.

1000 is a good value for most subjects, 1500 for slow subjects and when interocular data is required.

Remarks

This function is equivalent to
getEYELINK().sendCommand("automatic_calibration_pacing=%d"%(time))

© SR Research Ltd. 2003-2024



44 Class Documentation

Parameters

pace shortest delay.

*******3 readIOPort()

def readIOPort (
self,
ioport )

Sends a command to the tracker to read the specified io port.

Parameters

ioport port id of the io port.

*******4 writeIOPort()

def writeIOPort (
self,
ioport,
data )

Sends a command to the tracker to write the specified io port.

Parameters

ioport byte hardware I/O port address. The port address for the C and D ports on the EyeLink analog output
card are 4 and 5, respectively; the print port address will typically be 0x378 (please see the buttons.ini
settings).

data data to write

*******5 setHeuristicLinkAndFileFilter()

def setHeuristicLinkAndFileFilter (
self,
linkfilter,
filefilter = -1 )

EyeLink II only: Can be used to set level of filtering on the link and analog output, and on file data.

An additional delay of 1 sample is added to link or analog data for each filter level.

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 45

If an argument of <on> is used, link filter level is set to 1 to match EyeLink I delays.
The file filter level is not changed unless two arguments are supplied.
The default file filter level is 2.

Remarks

This function is equivalent to
if(getEYELINK().getTrackerVersion() >=2):

if(filefilter == -1):
getEYELINK().sendCommand("heuristic_filter %d"%(linkfilter))

else:
getEYELINK().sendCommand(" %d %d"%(linkfilter, filefilter));

Parameters

linkfilter Filter level of the link data. 0 or OFF disables link filter. 1 or ON sets filter to 1 (moderate filtering, 1
sample delay). 2 applies an extra level of filtering (2 sample delay).

filefilter Filter level of the data written to EDF file. 0 or OFF disables link filter. 1 or ON sets filter to 1
(moderate filtering, 1 sample delay). 2 applies an extra level of filtering (2 sample delay).

*******6 setHeuristicFilterOn()

def setHeuristicFilterOn (
self )

EyeLink 1 Only: Can be used to enable filtering, increases system delay by 4 msec if the filter was originally off.

NEVER TURN OFF THE FILTER WHEN ANTIREFLECTION IS TURNED ON.
For EyeLink II and newer eye tracker models, you should use the setHuresticFileAndLinkFilter()
method instead.

Remarks

This function is equivalent to
getEYELINK().sendCommand("heuristic_filter=ON");

*******7 setHeuristicFilterOff()

def setHeuristicFilterOff (
self )

EyeLink 1 Only: Can be used to disable filtering, reduces system delay by 4 msec.

NEVER TURN OFF THE FILTER WHEN ANTIREFLECTION IS TURNED ON. For EyeLink II and newer eye tracker
models, you should use the following setHuresticFileAndLinkFilter() method instead.

Remarks

This function is equivalent to
getEYELINK().sendCommand("heuristic_filter = OFF");

© SR Research Ltd. 2003-2024



46 Class Documentation

*******8 setPupilSizeDiameter()

def setPupilSizeDiameter (
self,
value )

Can be used to determine pupil size information to be recorded.

Remarks

This function is equivalent to
getEYELINK().sendCommand("pupil_size_diameter = %s"%(value));

Parameters

value YES to convert pupil area to diameter; NO to output pupil area data.

*******9 setSimulationMode()

def setSimulationMode (
self,
value )

Can be used to turn off head tracking if not used.

Do this before calibration.

Remarks

This function is equivalent to
getEYELINK().sendCommand("simulate_head_camera = %s"%(value));

Parameters

value YES to disable head tracking; NO to enable head tracking.

*******0 setScreenSimulationDistance()

def setScreenSimulationDistance (
self,
distance )

Used to compute correct visual angles and velocities when head tracking not used.

Remarks

This function is equivalent to
getEYELINK().sendCommand("simulation_screen_distance = %s"%(distance));

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 47

Parameters

distance simulated distance from display to subject in millimeters.

*******1 markPlayBackStart()

def markPlayBackStart (
self )

Marks the location in the data file from which playback will begin at the next call to EYEYLINK.startPlay←↩

Back().

When this command is not used (or on older tracker versions), playback starts from the beginning of the previous
recording block. This default behavior is suppressed after this command is used, until the tracker application is shut
down.

Remarks

This function is equivalent to
getEYELINK().sendCommand("mark_playback_start");

*******2 setNoRecordEvents()

def setNoRecordEvents (
self,
message = False,
button = False,
inputevent = False )

Selects what types of events can be sent over the link while not recording (e.g between trials).

This command has no effect for EyeLink II, and messages cannot be enabled for versions of EyeLink I before v2.1.

Remarks

This function is equivalent to
re = []
if(message):

re.append("MESSAGE ")
if(button):

re.append("BUTTON ")
if(inputevent):

re.append("INPUT ")
getEYELINK().sendCommand("link_nonrecord_events = %s"%"".join(re));

Parameters

message 1 to enable the recording of EyeLink messages.
button 1 to enable recording of buttons (1..8 press or release).
inputevent 1 to enable recording of changes in input port lines.

© SR Research Ltd. 2003-2024



48 Class Documentation

*******3 setFileSampleFilter()

def setFileSampleFilter (
self,
list )

Sets data in samples written to EDF file.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("file_sample_data = %s"%list)

Parameters

list list of the following data types, separated by spaces or commas.
GAZE screen x/y (gaze) position
GAZERES units-per-degree screen resolution
HREF head-referenced gaze
PUPIL raw eye camera pupil coordinates
AREA pupil area
STATUS warning and error flags
BUTTON button state and change flags
INPUT input port data lines

*******4 setFileEventData()

def setFileEventData (
self,
list )

Sets data in events written to EDF file.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("file_event_data = %s"%list);

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 49

Parameters

list list of the following event data types, separated by spaces or commas.
GAZE screen xy (gaze) position
GAZERES units-per-degree angular resolution
HREF HREF gaze position
AREA pupil area or diameter
VELOCITY velocity of eye motion (avg, peak)
STATUS warning and error flags for event
FIXAVG include ONLY average data in ENDFIX events
NOSTART start events have no data, just time stamp

*******5 setFileEventFilter()

def setFileEventFilter (
self,
list )

Sets which types of events will be written to EDF file.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("file_event_filter = %s"%list);

Parameters

list list of the following event types, separated by spaces or commas.
LEFT, RIGHT events for one or both eyes
FIXATION fixation start and end events
FIXUPDATE fixation (pursuit) state updates
SACCADE saccade start and end
BLINK blink start an end
MESSAGE messages (user notes in file)
BUTTON button 1..8 press or release
INPUT changes in input port lines;

*******6 setLinkSampleFilter()

def setLinkSampleFilter (
self,
list )

© SR Research Ltd. 2003-2024



50 Class Documentation

Sets data in samples sent through link.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("link_sample_data = %s"%list)

Parameters

list list of data types, separated by spaces or commas.
GAZE screen xy (gaze) position
GAZERES units-per-degree screen resolution
HREF head-referenced gaze
PUPIL raw eye camera pupil coordinates
AREA pupil area
STATUS warning and error flags
BUTTON button state and change flags
INPUT input port data lines

*******7 setLinkEventData()

def setLinkEventData (
self,
list )

Sets data in events sent through link.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("link_event_data = %s"%list);

Parameters

list list of data types, separated by spaces or commas.
GAZE screen xy (gaze) position
GAZERES units-per-degree angular resolution
HREF HREF gaze position
AREA pupil area or diameter
VELOCITY velocity of eye motion (avg, peak)
STATUS warning and error flags for event
FIXAVG include ONLY average data in ENDFIX events
NOSTART start events have no data, just time stamp

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 51

*******8 setLinkEventFilter()

def setLinkEventFilter (
self,
list )

Sets which types of events will be sent through link.

See tracker file "DATA.INI" for types.

Remarks

This function is equivalent to
getEYELINK().sendCommand("link_event_filter = %s"%list);

Parameters

list list of event types.

LEFT, RIGHT

events for one or both eyes

FIXATION

fixation start and end events

FIXUPDATE

fixation (pursuit) state updates

SACCADE

saccade start and end

BLINK

blink start an end

MESSAGE

messages (user notes in file)

BUTTON

button 1-8 press or release

INPUT

changes in input port lines;

© SR Research Ltd. 2003-2024



52 Class Documentation

*******9 setSaccadeVelocityThreshold()

def setSaccadeVelocityThreshold (
self,
vel )

Sets velocity threshold of saccade detector: usually 30 for cognitive research, 22 for pursuit and neurological work.

Remarks

This function is equivalent to
getEYELINK().sendCommand("saccade_velocity_threshold =%d"%(vel));

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 53

Parameters

vel minimum velocity (°/sec) for saccades.

*******0 setAccelerationThreshold()

def setAccelerationThreshold (
self,
accel )

Sets acceleration threshold of saccade detector: usually 9500 for cognitive research, 5000 for pursuit and neuro-
logical work.

Remarks

This function is equivalent to
getEYELINK().sendCommand("saccade_acceleration_threshold =%d"%(accl));

Parameters

accel minimum acceleration (°/sec/sec) for saccades.

*******1 setMotionThreshold()

def setMotionThreshold (
self,
deg )

Sets a spatial threshold to shorten saccades.

Usually 0.15 for cognitive research, 0 for pursuit and neurological work.

Remarks

This function is equivalent to
getEYELINK().sendCommand("saccade_motion_threshold =%d"%(deg));

Parameters

deg minimum motion (degrees) out of fixation before saccade onset allowed.

© SR Research Ltd. 2003-2024



54 Class Documentation

*******2 setPursuitFixup()

def setPursuitFixup (
self,
maxvel )

Sets the maximum pursuit velocity accommodation by the saccade detector.

Usually set to 60.

Remarks

This function is equivalent to
getEYELINK().sendCommand("saccade_pursuit_fixup = %d"%(v));

Parameters

maxvel maximum pursuit velocity fixup (°/sec).

*******3 setUpdateInterval()

def setUpdateInterval (
self,
time )

Normally set to 0 to disable fixation update events.

Set to 50 or 100 milliseconds to produce updates for gaze-controlled interface applications.

Remarks

This function is equivalent to
getEYELINK().sendCommand("fixation_update_interval = %d"%(time));

Parameters

time milliseconds between fixation updates, 0 turns off.

*******4 setFixationUpdateAccumulate() [1/2]

def setFixationUpdateAccumulate (
self,
time )

Normally set to 0 to disable fixation update events.

Set to 50 or 100 milliseconds to produce updates for gaze-controlled interface applications. Set to 4 to collect
single sample rather than average position.

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 55

Remarks

This function is equivalent to
getEYELINK().sendCommand("fixation_update_accumulate = %d"%(time));

Parameters

time milliseconds to collect data before fixation update for average gaze position.

*******5 setRecordingParseType()

def setRecordingParseType (
self,
rtype = "GAZE" )

Sets how velocity information for saccade detection is computed.

Remarks

This function is equivalent to
getEYELINK().sendCommand("recording_parse_type %s"%(rtype));

Parameters

rtype GAZE or HREF; Almost always left to GAZE.

*******6 drawText()

def drawText (
self,
text,
pos = (-1,-1) )

Draws text, coordinates are gaze-position display coordinates.

Remarks

This function is equivalent to
getEYELINK().sendCommand("print_position= %d %d"%pos)
getEYELINK().sendCommand("echo %s"%(text))

Parameters

text text to print in quotes.
pos Center point of text; Default position is (-1, -1).

© SR Research Ltd. 2003-2024



56 Class Documentation

*******7 clearScreen()

def clearScreen (
self,
color )

Clear tracker screen for drawing background graphics or messages.

Remarks

This function is equivalent to
getEYELINK().sendCommand("clear_screen %d"%(color));

Parameters

color 0 to 15.

*******8 drawLine()

def drawLine (
self,
firstPoint,
secondPoint,
color )

Draws line, coordinates are gaze-position display coordinates.

Remarks

This function is equivalent to
getEYELINK().sendCommand("draw_line %d %d %d %d %d"%

(firstPoint[0],firstPoint[1],secondPoint[0],secondPoint[1], color));

Parameters

firstPoint a two-item tuple, containing the x, y coordinates of the start point.
secondPoint a two-item tuple, containing the x, y coordinates of the end point.
color 0 to 15.

*******9 drawBox()

def drawBox (
self,

© SR Research Ltd. 2003-2024



4.8 EyeLink Class Reference 57

x,
y,
width,
height,
color )

Draws an empty box, coordinates are gaze-position display coordinates.

Remarks

This function is equivalent to
getEYELINK().sendCommand("draw_box %d %d %d %d %d"%(x,y,x+width,y+height,color));

Parameters

x x coordinates for the top-left corner of the rectangle.
y y coordinates for the top-left corner of the rectangle.
width width of the filled rectangle.
height height of the filled rectangle.
color 0 to 15.

*******0 drawFilledBox()

def drawFilledBox (
self,
x,
y,
width,
height,
color )

Draws a solid block of color, coordinates are gaze-position display coordinates.

Remarks

This function is equivalent to
getEYELINK().sendCommand("draw_filled_box %d %d %d %d %d"%(x,y,x+width,y+height,color));

Parameters

x x coordinates for the top-left corner of the rectangle.
y y coordinates for the top-left corner of the rectangle.
width width of the filled rectangle.
height height of the filled rectangle.
color 0 to 15.

© SR Research Ltd. 2003-2024



58 Class Documentation

*******1 getFixationUpdateInterval()

def getFixationUpdateInterval (
self )

Returns the fixation update interval value.

This does not query the tracker, only valid if setFixationUpdateInterval is called prior to calling this function.

*******2 getFixationUpdateAccumulate()

def getFixationUpdateAccumulate (
self )

Returns the fixation update accumulate value.

This does not query the tracker, only valid if setFixationUpdateAccumulate is called prior to calling this function.

*******3 setFixationUpdateInterval()

def setFixationUpdateInterval (
self,
interval )

Sends a command to the tracker to update the FixationUpdateInterval.

Parameters

interval value for fixation update interval

*******4 setFixationUpdateAccumulate() [2/2]

def setFixationUpdateAccumulate (
self,
accumulate )

Sends a command to the tracker to update the FixationUpdateAccumulate.

Parameters

accumulate value for fixation update accumulate

*******5 echo()

def echo (

© SR Research Ltd. 2003-2024



4.9 EyeLinkAddress Class Reference 59

self,
text,
pos = (-1,-1) )

Prints text at current print position to tracker screen, gray on black only.

Parameters

text text to print in quotes.
pos position of the text to display

Remarks

This function is equivalent to
getEYELINK().sendCommand("echo %s"%text)

*******6 drawCross()

def drawCross (
self,
x,
y,
color )

Draws a small "+" to mark a target point.

Remarks

This function is equivalent to
getEYELINK().sendCommand("draw_cross %d %d %d"%(x,y, color));

Parameters

x x coordinates for the center point of cross.
y y coordinates for the center point of cross.
color 0 to 15 (0 for black; 1 for blue; 2 for green; 3 for cyan; 4 for red; 5 for magenta; 6 for brown; 7 for light

gray; 8 for dark gray; 9 for light blue; 10 for light green; 11 for light cyan; 12 for light red; 13 for bright
magenta; 14 for yellow; 15 for bright white).

4.9 EyeLinkAddress Class Reference

The EyeLinkAddress class is used to hold addresses to EyeLink nodes.

Inherited by EyelinkMessage.

© SR Research Ltd. 2003-2024



60 Class Documentation

Public Member Functions

• def __init__ (self, ip=(100, 1, 1, 1), port=4000)
Constructor.

• def getIP (self)
Returns the IP address of the EyeLink node.

• def getPort (self)
Returns the port number of the EyeLink node.

4.9.1 Detailed Description

The EyeLinkAddress class is used to hold addresses to EyeLink nodes.

An instance of EyeLinkAddress class can be initialized with the class constructor:
EyeLinkAddress(ip = (100,1,1,1), port = 4000)

where ip is a four-item tuple containing the IP address of the EyeLink node and port is the port number of the
connection.

For example,
myAddress = EyeLinkAddress((100, 1, 1, 1), 4000)

4.9.2 Constructor & Destructor Documentation

******* __init__()

def __init__ (
self,
ip = (100,1,1,1),
port = 4000 )

Constructor.

Parameters

ip optional ipaddress in tuple form. eg. if the ip address is *************, the tuple form is (192,168,25,48)
The default ip address is *********

port optional port value as integer. The default value is 4000.

4.9.3 Member Function Documentation

******* getIP()

def getIP (
self )

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 61

Returns the IP address of the EyeLink node.

Returns

A four-item tuple (integer) containing the IP address of the EyeLink node.

******* getPort()

def getPort (
self )

Returns the port number of the EyeLink node.

Returns

An integer for the port number of the connection.

4.10 EyeLinkCBind Class Reference

Inherited by EyeLinkListener.

Public Member Functions

• def calculateOverallVelocityAndAcceleration ()
• def getTrackerVersion ()
• def nodeSendMessage ()
• def getkey ()
• def trackerTimeUsecOffset ()
• def startPlayBack ()
• def doTrackerSetup ()
• def inSetup ()
• def stopData ()
• def receiveDataFile ()
• def readKeyQueue ()
• def sendCommand ()
• def reset ()
• def openDataFile ()
• def sendTimedCommandEx ()
• def eyeAvailable ()
• def userMenuSelection ()
• def dummy_open ()
• def calculateVelocityXY ()
• def imageModeDisplay ()
• def requestTime ()
• def calculateVelocity ()
• def waitForBlockStart ()
• def echo_key ()
• def sendMessage ()

© SR Research Ltd. 2003-2024



62 Class Documentation

• def getNode ()
• def readKeyButton ()
• def bitmapBackdrop ()
• def pollTrackers ()
• def close ()
• def key_message_pump ()
• def closeDataFile ()
• def getNextData ()
• def acceptTrigger ()
• def startRecording ()
• def getLastMessage ()
• def readReply ()
• def trackerTimeUsec ()
• def getEventDataFlags ()
• def dataSwitch ()
• def isRecording ()
• def readRequest ()
• def getTrackerMode ()
• def sendKeybutton ()
• def startSetup ()
• def quietMode ()
• def getModeData ()
• def nodeSend ()
• def terminalBreak ()
• def startData ()
• def pollResponses ()
• def getLastButtonStates ()
• def isConnected ()
• def waitForData ()
• def broadcastOpen ()
• def getRecordingStatus ()
• def open ()
• def targetModeDisplay ()
• def getCalibrationResult ()
• def nodeReceive ()
• def getDataCount ()
• def getLastData ()
• def getSample ()
• def doDriftCorrect ()
• def setOfflineMode ()
• def getNewestSample ()
• def breakPressed ()
• def setName ()
• def getCurrentMode ()
• def resetData ()
• def getCalibrationMessage ()
• def pollRemotes ()
• def sendDataFile ()
• def getTrackerVersionString ()
• def escapePressed ()
• def pumpMessages ()
• def getSampleDataFlags ()
• def stopRecording ()
• def getButtonStates ()
• def abort ()

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 63

• def stopPlayBack ()
• def sendTimedCommand ()
• def getLastButtonPress ()
• def getkeyEx ()
• def isInDataBlock ()
• def nodeRequestTime ()
• def applyDriftCorrect ()
• def setAddress ()
• def readTime ()
• def getImageCrossHairData ()
• def bitmapSaveAndBackdrop ()
• def getEventTypeFlags ()
• def trackerTimeOffset ()
• def getPositionScalar ()
• def waitForModeReady ()
• def exitCalibration ()
• def openNode ()
• def flushKeybuttons ()
• def commandResult ()
• def getFloatData ()
• def getTargetPositionAndState ()
• def startDriftCorrect ()
• def trackerTime ()

4.10.1 Detailed Description

C Implementation of EyeLinkListener.

4.10.2 Member Function Documentation

******** calculateOverallVelocityAndAcceleration()

def calculateOverallVelocityAndAcceleration ( )

Calculates overall velocity and acceleration for left and right eyes separately.

Parameters

in slen Sample model to use for velocity calculation. Acceptable models are FIVE_SAMPLE_MODEL,
NINE_SAMPLE_MODEL, SEVENTEEN_SAMPLE_MODEL and EL1000_TRACKER_MODEL.

Returns

A list with 3 elements

• The first element of the list:

© SR Research Ltd. 2003-2024



64 Class Documentation

– overall velocity for left and right eye. Upon return of this function,vel[0] will contain overall velocity
for left eye and vel[1] will contain overall velocity for right eye. If velocity cannot be calculated for
any reason(eg. insufficient samples, no data) MISSING_DATA is filled for the given velocity.

• The second element of the list:
– overall acceleration for left and right eye. Upon return of this function, acc[0] will contain overall

acceleration for left eye and acc[1] will contain overall acceleration for right eye. If acceleration
cannot be calculated for any reason(eg. insufficient samples, no data) MISSING_DATA is filled for
the given acceleration.

• The third element of the list:
– vel_sample Velocity for sample.

******** getTrackerVersion()

def getTrackerVersion ( )

After connection, determines if the connected tracker is an EyeLink I or II. Use getTrackerVersionString()
to get the string value.

Remarks

This is equivalent to the C API
INT16 eyelink_get_tracker_version(char *c);

Returns

The returned value is a number (0 if not connected, 1 for EyeLink I, 2 for EyeLink II).

******** nodeSendMessage()

def nodeSendMessage ( )

Sends a text message the connected eye tracker. The text will be added to the EDF file.

Remarks

If the link is initialized but not connected to a tracker, the message will be sent to the tracker set by
setAddress() of the pylink module. This function is equivalent to the C API
INT16 eyelink_node_send_message(ELINKADDR node, char *msg);

Parameters

address Address of a specific tracker.
message Text to send to the tracker.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 65

Returns

0 if no error, else link error code.

******** getkey()

def getkey ( )

Returns the key pressed.

Remarks

Warning: This function processes and dispatches any waiting messages. This will allow Windows to perform
disk access and negates the purpose of realtime mode. Usually these delays will be only a few millisec-
onds, but delays over 20 milliseconds have been observed. You may wish to call escapePressed() or
breakPressed() in recording loops instead of getkey() if timing is critical, for example in a gaze-
contingent display. Under Windows XP, these calls will not work in realtime mode at all (although these do
work under Windows 2000). Under Windows 95/98/Me, realtime performance is impossible even with this
strategy. Some useful keys are:

• CURS_UP
• CURS_DOWN
• CURS_LEFT
• CURS_RIGHT
• ESC_KEY
• ENTER_KEY
• TERMINATE_KEY
• JUNK_KEY

This function is equivalent to the C API
unsigned getkey(void);

Returns

0 if no key pressed, else key code. TERMINATE_KEY if CTRL-C held down or program has been terminated.

******** trackerTimeUsecOffset()

def trackerTimeUsecOffset ( )

Returns the time difference between the tracker time and display pc time.

Remarks

This is equivalent to the C API
double eyelink_time_usec_offset();

Returns

A double precision data for the time difference (in microseconds) between the tracker time and display pc time.

© SR Research Ltd. 2003-2024



66 Class Documentation

******** startPlayBack()

def startPlayBack ( )

Flushes data from queue and starts data playback. An EDF file must be open and have at least one recorded trial.
Use waitForData() method to wait for data: this will time out if the playback failed. Playback begins from start
of file or from just after the end of the next-but-last recording block. Link data is determined by file contents, not by
link sample and event settings.

Remarks

This function is equivalent to the C API
INT16 eyelink_playback_start(void);

Returns

0 if command sent fine, else link error.

******** doTrackerSetup()

def doTrackerSetup ( )

Switches the EyeLink tracker to the Setup menu, from which camera setup, calibration, validation, drift correction,
and configuration may be performed. Pressing the 'ESC' key on the tracker keyboard will exit the Setup menu
and return from this function. Calling exitCalibration() from an event handler will cause any call to do_←↩

tracker_setup() in progress to return immediately.

Parameters

width Width of the screen.
height Height of he screen.

******** inSetup()

def inSetup ( )

Checks if tracker is still in a Setup menu activity (includes camera image view, calibration, and validation). Used to
terminate the subject setup loop.

Remarks

This function is equivalent to the C API
INT16 eyelink_in_setup(void);

Returns

0 if no longer in setup mode.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 67

4.10.2.9 stopData()

def stopData ( )

Places tracker in idle (off-line) mode, does not flush data from queue.

Remarks

Should be followed by a call to waitForModeReady() method. This function is equivalent to the C API
INT16 eyelink_data_stop(void);

Returns

0 if command sent fine, else link error.

********0 receiveDataFile()

def receiveDataFile ( )

This receives a data file from the EyeLink tracker PC. Source file name and destination file name should be given.

Remarks

This function is equivalent to the C API
int receive_data_file(char *src, char *dest, int is_path=0);

Parameters

src Name of eye tracker file (including extension).
dest Name of local file to write to (including extension).

Returns

Size of file if successful, Otherwise Runtime Exception is raised.

********1 readKeyQueue()

def readKeyQueue ( )

Read keys from the key queue. It is similar to getkey(), but does not process Windows messages. This can be
used to build key-message handlers in languages other than C.

Remarks

This function is equivalent to the C API
UINT16 read_getkey_queue(void);

© SR Research Ltd. 2003-2024



68 Class Documentation

Returns

0 if no key pressed.
JUNK_KEY (1) if untranslatable key.
TERMINATE_KEY (0x7FFF) if CTRL-C is pressed, terminal_break() was called, or the program
has been terminated with ALT-F4.
or code of key if any key pressed.

********2 sendCommand()

def sendCommand ( )

Sends the given command to connected eyelink tracker and returns the command result.

Remarks

This is equivalent to the C API
int eyecmd_printf(char *fmt, ...); // without any formatting.

Parameters

command_text Text command to be sent. It does not support printf() kind of formatting.

Returns

Command result. If there is any problem sending the command, a runtime exception is raised.

********3 reset()

def reset ( )

Sends a reset message to the EyeLink tracker.

Remarks

This function is equivalent to the C API
INT16 eyelink_close(int send_msg); // with send_msg parameter 0.

Returns

0 if successful, otherwise link error.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 69

********4 openDataFile()

def openDataFile ( )

Opens a new EDF file on the EyeLink tracker computer's hard disk. By calling this function will close any currently
opened file. This may take several seconds to complete. The file name should be formatted for MS-DOS, usually 8
or less characters with only 0-9, A-Z, and '_' allowed.

Remarks

This function is equivalent to the C API
int open_data_file(char *name);

Parameters

name Name of eye tracker file, 8 characters or less.

Returns

0 if file was opened successfully else error code.

********5 sendTimedCommandEx()

def sendTimedCommandEx ( )

Sends a command to the connected eye tracker, wait for reply.

Returns

List of 2 items. The first item contains the return value of eyelink_timed_command(). The second item
contains a string description of the error message.

Remarks

If there is an error, no exception is raised.

See also

sendTimedCommand()

© SR Research Ltd. 2003-2024



70 Class Documentation

********6 eyeAvailable()

def eyeAvailable ( )

After calling the waitForBlockStart() method, or after at least one sample or eye event has been read, this
function can be used to check which eyes data is available for.

Remarks

This is equivalent to the C API
INT16 eyelink_eye_available(void);

Returns

LEFT_EYE (0) if left eye data.
RIGHT_EYE (1) if right eye data.
BINOCULAR (2) if both left and right eye data.
-1 if no eye data is available.

********7 userMenuSelection()

def userMenuSelection ( )

Checks for a user-menu selection, clears response for next call.

Remarks

This function is equivalent to the C API
INT16 eyelink_user_menu_selection(void);

Returns

0 if no selection made since last call, else code of selection.

********8 dummy_open()

def dummy_open ( )

Sets the EyeLink library to simulate an eyetracker connection. Functions will return plausible values, but no data.

Remarks

The function isConnected() will return -1 to indicate a simulated connection.

********9 calculateVelocityXY()

def calculateVelocityXY ( )

Calculates left x velocity, left y velocity, right x velocity and right y velocity from queue of samples.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 71

Parameters

in slen Sample model to use for velocity calculation. Acceptable models are FIVE_SAMPLE_MODEL,
NINE_SAMPLE_MODEL, SEVENTEEN_SAMPLE_MODEL and EL1000_TRACKER_MODEL.

Returns

A list with 3 elements

• The first element of the list:

– leftvel x and y velocity for left eye. The float tuple of 2 filled with x and y velocity values. Upon return
of this function leftvel[0] contains the left x velocity data and leftvel[1] contains left y velocity data. If
velocity cannot be calculated for any reason (eg. insufficient samples, no data) MISSING_DATA is
filled for the given velocity.

• The second element of the list:

– rightvel x and y velocity for right eye. The float tuple of 2 filled with x and y velocity values. Upon
return of this function rightvel[0] contains the right x velocity data and rightvel[1] contains right y
velocity data. If velocity cannot be calculated for any reason (eg. insufficient samples, no data)
MISSING_DATA is filled for the given velocity.

• The third element of the list:

– vel_sample Velocity for sample.

********0 imageModeDisplay()

def imageModeDisplay ( )

This handles display of the EyeLink camera images. While in imaging mode, it continuously requests and displays
the current camera image. It also displays the camera name and threshold setting. Keys on the subject PC keyboard
are sent to the tracker, so the experimenter can use it during setup. It will exit when the tracker leaves imaging mode
or disconnects.

Returns

0 if OK, TERMINATE_KEY if pressed, -1 if disconnect.

Remarks

This function not normally used externally. If you need camera setup use doTrackerSetup() or if you need drift
correction use doDriftCorrect()

© SR Research Ltd. 2003-2024



72 Class Documentation

********1 requestTime()

def requestTime ( )

Sends a request the connected eye tracker to return its current time.

Remarks

The time reply can be read with readTime().

Returns

0 if no error, else link error code.

See also

rackerTime()

********2 calculateVelocity()

def calculateVelocity ( )

Calculates overall velocity for left and right eyes separately.

Parameters

in slen Sample model to use for velocity calculation. Acceptable models are FIVE_SAMPLE_MODEL,
NINE_SAMPLE_MODEL, SEVENTEEN_SAMPLE_MODEL and EL1000_TRACKER_MODEL.

Returns

A list with 3 elements:

• First two elements of the list:
– Upon return of this function, vel[0] will contain overall velocity for left eye and vel[1] will contain

overall velocity for right eye. If velocity cannot be calculated for any reason(eg. insufficient samples,
no data) MISSING_DATA is filled for the given velocity.

• Third element of the list:
– vel_sample Velocity for sample.

********3 waitForBlockStart()

def waitForBlockStart ( )

Reads and discards events in data queue until in a recording block. Waits for up to <timeout> milliseconds for
a block containing samples, events, or both to be opened. Items in the queue are discarded until the block start
events are found and processed. This function will fail if both samples and events are selected but only one of link
samples and events were enabled by startRecording().

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 73

Remarks

This function did not work in versions previous to 2.0. This function is equivalent to the C API
INT16 eyelink_wait_for_block_start(UINT32 maxwait,INT16 samples, INT16 events);

Parameters

timeout Time in milliseconds to wait.
samples If non-zero, check if in a block with samples.
events If non-zero, check if in a block with events.

Exceptions

Runtime exception is raised if time expires and no data of masked types is available. handling:
getLastError()[0]==0

Returns

true if data is available

********4 echo_key()

def echo_key ( )

Checks for Windows keystroke events and dispatches messages; similar to getkey(), but also sends keystroke
to tracker.

Remarks

Warning: Under Windows XP, this call will not work in realtime mode at all, and will take several seconds to
respond if graphics are being drawn continuously. This function works well in realtime mode under Windows
2000. This function is equivalent to the C API
unsigned echo_key(void);

Returns

0 if no key pressed, else key code TERMINATE_KEY if CTRL-C held down or program has been terminated.

********5 sendMessage()

def sendMessage ( )

Sends the given message to the connected eyelink tracker. The message will be written to the eyelink tracker.

Remarks

This is equivalent to the C API
int eyemsg_printf(char *fmt, ...);

© SR Research Ltd. 2003-2024



74 Class Documentation

Parameters

message_text Text message to be sent. It does not support printf() kind of formatting.

Returns

If there is any problem sending the message, a runtime exception is raised.

********6 getNode()

def getNode ( )

Reads the responses returned by other trackers or remotes in response to pollTrackers() or
pollRemotes(). It can also read the tracker broadcast address and remote broadcast addresses.

Remarks

This function is equivalent to the C API
INT16 eyelink_get_node(INT16 resp, void *data);

Parameters

resp Number of responses to read: 0 gets our data, 1 get first response, 2 gets the second response, etc.
-1 to read the tracker broadcast address. -2 to read remote broadcast addresses.

Returns

If successful, an instance of EyeLinkMessage class returned.

********7 readKeyButton()

def readKeyButton ( )

Reads any queued key or button events from tracker.

Remarks

This function is equivalent to the C API
UINT16 eyelink_read_keybutton(INT16 *mods,INT16 *state, UINT16 *kcode, UINT32 *time);

Returns

A five-item tuple, recording (in the following order):

• Key character if key press/release/repeat, KB_BUTTON (0xFF00) if button press or release,
• Button number or key modifier (Shift, Alt and Ctrl key states),
• Key or button change (KB_PRESS, KB_RELEASE, or KB_REPEAT),
• Key scan code,
• Tracker time of the key or button change.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 75

********8 bitmapBackdrop()

def bitmapBackdrop ( )

This function transfers the bitmap to the tracker PC as backdrop for gaze cursors.

Parameters

iwidth Original image width.
iheight Original image height.
pixels Pixels of the image in one of two possible formats: pixel=[line1, line2, ... linen]

line=[pix1,pix2,...,pixn],pix=(r,g,b). pixel=[line1, line2, ... linen]
line=[pix1,pix2,...,pixn],pix=0xAARRGGBB.

xs Crop x position.
ys Crop y position.
width Crop width.
height Crop height.
xd X position - transfer.
yd Y position - transfer.
xferoptions Transfer options(BX_AVERAGE,BX_DARKEN,BX_LIGHTEN,BX_MAXCONTRAST,BX_N←↩

ODITHER,BX_GRAYSCALE). Transfer options set with bitwise OR of the following constants,
determines how bitmap is processed:

• BX_AVERAGE Averaging combined pixels

• BX_DARKEN Choosing darkest and keep thin dark lines.

• BX_LIGHTEN Choosing darkest and keep thin white lines and control how bitmap size is
reduced to fit tracker display.

• BX_MAXCONTRAST Maximizes contrast for clearest image.

• BX_NODITHER Disables the dithering of the image.

• BX_GREYSCALE Converts the image to grayscale (grayscale works best for EyeLink I,
text, etc.).

Remarks

This function should not be called when timing is critical, as this might take very long to return.

********9 pollTrackers()

def pollTrackers ( )

Asks all trackers (with EyeLink software running) on the network to send their names and node address.

Remarks

This function is equivalent to the C API
INT16 eyelink_poll_trackers(void);

Returns

0 if successful, otherwise link error.

© SR Research Ltd. 2003-2024



76 Class Documentation

********0 close()

def close ( )

Sends a disconnect message to the EyeLink tracker.

Remarks

This function is equivalent to the C API
INT16 eyelink_close(int send_msg); // with send_msg parameter 1.

Returns

0 if successful, otherwise link error.

********1 key_message_pump()

def key_message_pump ( )

Similar to pumpMessages(), but only processes keypresses. This may help reduce latency.

********2 closeDataFile()

def closeDataFile ( )

Closes any currently opened EDF file on the EyeLink tracker computer's hard disk. This may take several seconds
to complete.

Remarks

This function is equivalent to the C API
int close_data_file(void);

Returns

0 if command executed successfully else error code.

********3 getNextData()

def getNextData ( )

Fetches next data item from link buffer and returns the data item type. If the item is not wanted, simply ignore it.
Otherwise, call getFloatData() to read it into a buffer.

Returns

0 if no data, SAMPLE_TYPE if sample, else event type.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 77

********4 acceptTrigger()

def acceptTrigger ( )

Triggers the EyeLink tracker to accept a fixation on a target, similar to the 'Enter' key or spacebar on the tracker.

Remarks

This function is equivalent to the C API
INT16 eyelink_accept_trigger(void);

Returns

NO_REPLY if drift correction not completed yet.
OK_RESULT (0) if success.
ABORT_REPLY (27) if 'ESC' key aborted operation.
-1 if operation failed.
1 if poor calibration or excessive validation error.

********5 startRecording()

def startRecording ( )

Starts the EyeLink tracker recording, sets up link for data reception if enabled.

Remarks

Recording may take 10 to 30 milliseconds to begin from this command. The function also waits until at least
one of all requested link data types have been received. If the return value is not zero, return the result as the
trial result code. This is equivalent to the C API
INT16 start_recording(INT16 file_samples, INT16 file_events, INT16 link_samples, INT16 link_events);

Parameters

file_samples If 1, writes samples to EDF file. If 0, disables sample recording.
file_events If 1, writes events to EDF file. If 0, disables event recording.
link_samples If 1, sends samples through link. If 0, disables link sample access.
link_events If 1, sends events through link. If 0, disables link event access.

Returns

0 if successful, else trial return code.

********6 getLastMessage()

def getLastMessage ( )

Returns text associated with last command response: may have error message.

© SR Research Ltd. 2003-2024



78 Class Documentation

Remarks

This is equivalent to the C API
INT16 eyelink_last_message(char *buf);

Returns

Text associated with last command response or None.

********7 readReply()

def readReply ( )

Returns text with reply to last read request.

Remarks

This is equivalent to the C API
INT16 eyelink_read_reply(char *buf);

Returns

String to contain text or None.

********8 trackerTimeUsec()

def trackerTimeUsec ( )

Returns the current tracker time (in microseconds) since the tracker application started.

Remarks

This is equivalent to the C API
UINT32 eyelink_tracker_time();

Returns

A double precision data for the current tracker time (in microseconds) since tracker initialization.

********* getEventDataFlags()

def getEventDataFlags ( )

Returns the event data content flags.

Remarks

This is equivalent to the C API
UINT16 eyelink_event_data_flags(void);

Returns

Possible return values are a set of the following bit flags:

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 79

Constant Name Value Description
EVENT_VELOCITY 0x8000 Has velocity data
EVENT_PUPILSIZE 0x4000 Has pupil size data
EVENT_GAZERES 0x2000 Has gaze resolution
EVENT_STATUS 0x1000 Has status flags
EVENT_GAZEXY 0x0400 Has gaze x, y position
EVENT_HREFXY 0x0200 Has head-ref x, y position
EVENT_PUPILXY 0x0100 Has pupil x, y position
FIX_AVG_ONLY 0x0008 Only average data to fixation events
START_TIME_ONLY 0x0004 Only start-time in start events
PARSEDBY_GAZE 0x00C0 Events were generated by GAZE data
PARSEDBY_HREF 0x0080 Events were generated by HREF data
PARSEDBY_PUPIL 0x0040 Events were generated by PUPIL data

********0 dataSwitch()

def dataSwitch ( )

Sets what data from tracker will be accepted and placed in queue.

Remarks

This does not start the tracker recording, and so can be used with broadcastOpen(). It also does not
clear old data from the queue. This function is equivalent to the C API
INT16 eyelink_data_switch(UINT16 flags);

Parameters

flags Bitwise OR of the following flags:

• RECORD_LINK_SAMPLES - send samples on link.

• RECORD_LINK_EVENTS - send events on link.

Returns

0 if no error, else link error code.

********1 isRecording()

def isRecording ( )

Check if we are recording: if not, report an error. Call this function while recording. It will return true if recording is
still in progress, otherwise it will throw an exception. It will also handle the EyeLink Abort menu. Any errors returned
by this function should be returned by the trial function. On error, this will disable realtime mode and restore the
heuristic.

© SR Research Ltd. 2003-2024



80 Class Documentation

Remarks

This function is equivalent to the C API
int check_recording(void);

Returns

TRIAL_OK (0) if no error.
REPEAT_TRIAL, SKIP_TRIAL, ABORT_EXPT, TRIAL_ERROR if recording aborted.

********* readRequest()

def readRequest ( )

Sends a text variable name whose value is read and returned by the tracker as a text string.

Remarks

If the link is initialized but not connected to a tracker, the message will be sent to the tracker set by
setAddress(). However, these requests will be ignored by tracker versions older than EyeLink I v2.1
and EyeLink II v1.1. This is equivalent to the C API
INT16 eyelink_read_request(char *text);

Parameters

text String with message to send.

Returns

0 if success, otherwise link error code.

********* getTrackerMode()

def getTrackerMode ( )

Returns raw EyeLink mode numbers.

Remarks

This function is equivalent to the C API
INT16 eyelink_tracker_mode(void);

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 81

Returns

Raw EyeLink mode, -1 if link disconnected.

Constant Value
EL_IDLE_MODE 1
EL_IMAGE_MODE 2
EL_SETUP_MENU_MODE 3
EL_USER_MENU_1 5
EL_USER_MENU_2 6
EL_USER_MENU_3 7
EL_OPTIONS_MENU_MODE 8
EL_OUTPUT_MENU_MODE 9
EL_DEMO_MENU_MODE 10
EL_CALIBRATE_MODE 11
EL_VALIDATE_MODE 12
EL_DRIFT_CORR_MODE 13
EL_RECORD_MODE 14

USER_MENU_NUMBER(mode) ((mode)-4)

********* sendKeybutton()

def sendKeybutton ( )

Sends a key or button event to tracker. Only key events are handled for remote control.

Remarks

This function is equivalent to the C API
INT16 eyelink_send_keybutton(UINT16 code, UINT16 mods, INT16 state);

Parameters

code Key character, or KB_BUTTON (0xFF00) if sending button event.
mods Button number, or key modifier (Shift, Alt and Ctrl key states).
state Key or button change (KB_PRESS or KB_RELEASE).

Returns

0 if OK, else send link error.

********5 startSetup()

def startSetup ( )

Switches the EyeLink tracker to the setup menu, for calibration, validation, and camera setup. Should be followed
by a call to waitForModeReady().

© SR Research Ltd. 2003-2024



82 Class Documentation

Remarks

This is equivalent to the C API
INT16 eyelink_start_setup(void);

Returns

0 if command send fine.

********6 quietMode()

def quietMode ( )

Controls the level of control an application has over the tracker.

Remarks

This function is equivalent to the C API
INT16 eyelink_quiet_mode(INT16 mode);

Parameters

mode 0 to allow all communication;
1 to block commands (allows only key presses, messages, and time or variable read requests);
2 to disable all commands, requests and messages;
-1 to just return current setting.

Returns

Returns the previous mode settings.

********7 getModeData()

def getModeData ( )

After calling waitForBlockStart(), or after at least one sample or eye event has been read, returns EyeLink
II extended mode data.

Remarks

This function is equivalent to the C API
INT16 eyelink2_mode_data(INT16 *sample_rate, INT16 *crmode, INT16 *file_filter, INT16 *link_filter);

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 83

Returns

A five-item tuple holding (in the following order):

• success value of call to eyelink2_mode_data(). 0 if link state data available, -1 otherwise.
• sampling rate (samples per second),
• CR mode flag (0 if pupil-only mode, else pupil-CR mode),
• filter level applied to file samples (0 = off, 1 = std, 2 = extra),
• filter level applied to link and analog output samples (0 = off, 1 = std, 2 = extra).

********* nodeSend()

def nodeSend ( )

Sends a given data to the given node.

Remarks

This function is equivalent to the C API
INT16 eyelink_node_send(ELINKADDR node, void *data, UINT16 dsize);

Parameters

addr the address of the node.
data Pointer to buffer containing data to send.
length Number of bytes of data.

Returns

0 if successful, otherwise link error.

********* terminalBreak()

def terminalBreak ( )

This function can be called in an event handler to signal that the program is terminating. Calling this function with
an argument of 1 will cause breakPressed() to return 1, and getkey() to return TERMINATE_KEY. These
functions can be re-enabled by calling terminalBreak() with an argument of 0.

Remarks

This function is equivalent to the C API
void terminal_break(INT16 assert);

© SR Research Ltd. 2003-2024



84 Class Documentation

Parameters

assert 1 to signal a program break, 0 to reset break.

********* startData()

def startData ( )

Switches tracker to Record mode, enables data types for recording to EDF file or sending to link. These types are
set with a bit wise OR of these flags:

Constant Name Value Description
RECORD_FILE_SAMPLES 1 Enables sample recording to EDF file
RECORD_FILE_EVENTS 2 Enables event recording to EDF file
RECORD_LINK_SAMPLES 4 Enables sending samples to the link
RECORD_LINK_EVENTS 8 Enables sending events to the link

Remarks

If <lock> is nonzero, the recording may only be terminated through stopRecording() or
stopData() method of the EyeLinkListener class, or by the Abort menu ('Ctrl' 'Alt' 'A' keys on the eye
tracker). If zero, the tracker 'ESC' key may be used to halt recording. This function is equivalent to the C API
INT16 eyelink_data_start(UINT16 flags, INT16 lock);

Parameters

flags Bitwise OR of flags to control what data is recorded. If 0, recording will be stopped.
lock If nonzero, prevents 'ESC' key from ending recording.

Returns

0 if command sent fine, else link error.

********* pollResponses()

def pollResponses ( )

Returns the count of node addresses received so far following the call of pollRemotes() or pollTrackers().

Remarks

You should allow about 100 milliseconds for all nodes to respond. Up to 4 node responses are saved. This
function is equivalent to the C API
INT16 eyelink_poll_responses(void);

Returns

Number of nodes responded. 0 if no responses.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 85

********* getLastButtonStates()

def getLastButtonStates ( )

Returns a flag word with bits set to indicate which tracker buttons are currently pressed. This is button 1 for the
LSB, up to button 16 for the MSB. Same as getButtonStates() except, the time is also returned.

Returns

a list of with values time and states of 7 buttons. Example:
v = eyelink.getLastButtonStates()
print "time of last button states ", v[0]
print "Button states"
button_states = v[1:]
button =0
for x in button_states:

button = button +1
if x!=0:

print "Button ",button," Pressed "

See also

eyelink_send_keybutton()

********3 isConnected()

def isConnected ( )

Checks whether the connection to the tracker is alive.

Remarks

This is equivalent to the C API
INT16 eyelink_is_connected(void);

Returns

0 if link closed.
-1 if simulating connection.
1 for normal connection.
2 for broadcast connection.

********4 waitForData()

def waitForData ( )

Waits for data to be received from the eye tracker. Can wait for an event, a sample, or either. Typically used after
record start to check if data is being sent.

Remarks

This function is equivalent to the C API
INT16 eyelink_wait_for_data (UINT32 maxwait, INT16 samples, INT16 events);

© SR Research Ltd. 2003-2024



86 Class Documentation

Parameters

maxwait Time in milliseconds to wait for data.
samples If 1, return when first sample available.
events If 1, return when first event available.

Exceptions

RuntimeError if time expires and no data of masked types is available. handling: getLastError()[0] == 0

Returns

1 if data is available.

********5 broadcastOpen()

def broadcastOpen ( )

Allows a third computer to listen in on a session between the eye tracker and a controlling remote machine. This
allows it to receive data during recording and playback, and to monitor the eye tracker mode. The local computer
will not be able to send commands to the eye tracker, but may be able to send messages or request the tracker
time.

Remarks

This may not function properly, if there are more than one Ethernet cards installed. This function is equivalent
to the C API
INT16 eyelink_broadcast_open(void);

Returns

0 if successful.
LINK_INITIALIZE_FAILED if link could not be established.
CONNECT_TIMEOUT_FAILED if tracker did not respond.
WRONG_LINK_VERSION if the versions of the EyeLink library and tracker are incompatible.

********* getRecordingStatus()

def getRecordingStatus ( )

Checks if we are in Abort menu after recording stopped and returns trial exit code. Call this function on leaving a
trial. It checks if the EyeLink tracker is displaying the Abort menu, and handles it if required. The return value from
this function should be returned as the trial result code.

Remarks

This function is equivalent to the C API
INT16 check_record_exit(void);

Returns

TRIAL_OK if no error.
REPEAT_TRIAL, SKIP_TRIAL, ABORT_EXPT if Abort menu activated.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 87

********* open()

def open ( )

Opens connection to single tracker. If no parameters are given, it tries to open connection to the default host
(*********).

Remarks

This is equivalent to the C API
INT16 eyelink_open_node(ELINKADDR node, INT16 busytest);

Parameters

eyelink_address Optional argument. Text IP address of the host PC (the default value is, "*********").
busytest Optional argument. If non-zero the call to eyelink_open_node() will not disconnect

an existing connection.

Returns

Throws Runtime error exception if it cannot open the connection.

********* targetModeDisplay()

def targetModeDisplay ( )

This function needs some "helper" graphics to clear the scren and draw the fixation targets. Since C graphics are
compiler-dependent, these are found in other C source files.

While tracker is in any mode with fixation targets... Reproduce targets tracker needs. (if local_trigger) Local Space-
bar acts as trigger. (if local_control) Local keys echoes to tracker.

Returns

0 if OK, 27 if aborted, TERMINATE_KEY if pressed..

********9 getCalibrationResult()

def getCalibrationResult ( )

Checks for a numeric result code returned by calibration, validation, or drift correction.

Remarks

This function is equivalent to the C API
INT16 eyelink_cal_result(void);

Returns

NO_REPLY if drift correction not completed yet.
OK_RESULT (0) if success.
ABORT_REPLY (27) if 'ESC' key aborted operation.
-1 if operation failed.
1 if poor calibration or excessive validation error.

© SR Research Ltd. 2003-2024



88 Class Documentation

********0 nodeReceive()

def nodeReceive ( )

Checks for and gets the last packet received, stores the data and the node address sent from.

Remarks

Data can only be read once, and is overwritten if a new packet arrives before the last packet has been read.
This function is equivalent to the C API
INT16 eyelink_node_receive(ELINKADDR node, void *data);

Returns

An instance of EyeLinkMessage class is returned, if successful.

********1 getDataCount()

def getDataCount ( )

Counts total items in queue: samples, events, or both.

Remarks

This function is equivalent to the C API
INT16 eyelink_data_count(INT16 samples, INT16 events);

Parameters

samples if non-zero count the samples.
events if non-zero count the events.

Returns

Total number of samples and events is in the queue.

********2 getLastData()

def getLastData ( )

Gets an integer (unconverted) copy of the last/newest link data (sample or event) seen by getNextData().

Remarks

This function is equivalent to the C API
INT16 eyelink_get_last_data(void *buf);

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 89

Returns

Object of type Sample or Event.

********3 getSample()

def getSample ( )

Gets an integer (unconverted) sample from end of queue, discards any events encountered.

Remarks

This is equivalent to the C API
INT16 eyelink_get_sample(void *sample);

Returns

Object of type Sample.

********4 doDriftCorrect()

def doDriftCorrect ( )

Performs a drift correction before a trial.

Remarks

This is equivalent to the C API
int do_drift_correct(int x, int y, int draw, int allow_setup);

Parameters

x X Position (in pixels) of drift correction target.
y Y Position (in pixels) of drift correction target.
draw If 1, the drift correction will clear the screen to the target background color, draw the target, and

clear the screen again when the drift correction is done. If 0, the fixation target must be drawn
by the user.

allow_setup If 1, accesses Setup menu before returning, else aborts drift correction.

Returns

0 if successful, 27 if 'ESC' key was pressed to enter Setup menu or abort.

© SR Research Ltd. 2003-2024



90 Class Documentation

********5 setOfflineMode()

def setOfflineMode ( )

Places EyeLink tracker in off-line (idle) mode. Wait till the tracker has finished the mode transition.

Remarks

This is equivalent to the C API
INT16 set_offline_mode(void);

********6 getNewestSample()

def getNewestSample ( )

Check if a new sample has arrived from the link. This is the latest sample, not the oldest sample that is read by
getNextData(), and is intended to drive gaze cursors and gaze-contingent displays.

Remarks

This function is equivalent to the C API
INT16 CALLTYPE eyelink_newest_float_sample(void FARTYPE *buf);

Returns

None if there is no sample, instance of Sample type otherwise.

********7 breakPressed()

def breakPressed ( )

Tests if the program is being interrupted. You should break out of loops immediately if this function does not return
0, if getkey() return TERMINATE_KEY, or if isConnected() method of the class returns 0.

Remarks

Under Windows XP, this call will not work in realtime mode at all, and will take several seconds to respond if
graphics are being drawn continuously. This function works well in realtime mode under Windows 2000. This
function is equivalent to the C API
INT16 break_pressed(void);

Returns

1 if CTRL-C is pressed, terminalBreak() was called, or the program has been terminated with ALT-F4;
0 otherwise

********8 setName()

def setName ( )

Sets the node name of this computer (up to 35 characters).

Remarks

This function is equivalent to the C API
INT16 eyelink_set_name(char *name);

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 91

Parameters

name String to become new name.

********9 getCurrentMode()

def getCurrentMode ( )

This function tests the current tracker mode, and returns a set of flags based of what the mode is doing. The most
useful flag using the EyeLink experiment toolkit is IN_USER_MENU to test if the EyeLink Abort menu has been
activated.

Remarks

This is equivalent to the C API
INT16 eyelink_current_mode(void);

Returns

Set of bit flags that mark mode function:

IN_DISCONNECT_MODE if disconnected
IN_IDLE_MODE if off-line (Idle mode)
IN_SETUP_MODE if in Setup-menu related mode
IN_RECORD_MODE if tracking is in progress
IN_PLAYBACK_MODE if currently playing back data
IN_TARGET_MODE if in mode that requires a fixation target
IN_DRIFTCORR_MODE if in drift-correction
IN_IMAGE_MODE if displaying grayscale camera image
IN_USER_MENU if displaying Abort or user-defined menu

********0 resetData()

def resetData ( )

Prepares link buffers to receive new data and removes old data from buffer.

********1 getCalibrationMessage()

def getCalibrationMessage ( )

Returns text associated with result of last calibration, validation, or drift correction. This usually specifies errors or
other statistics.

© SR Research Ltd. 2003-2024



92 Class Documentation

Remarks

This function is equivalent to the C API
INT16 eyelink_cal_message(char *msg);

Returns

Message string associated with result of last calibration, validation, or drift correction.

********2 pollRemotes()

def pollRemotes ( )

Asks all non-tracker computers (with EyeLink software running) on the network to send their names and node
address.

Remarks

This function is equivalent to the C API
INT16 eyelink_poll_ remotes(void);

Returns

0 if successful, otherwise link error.

********3 sendDataFile()

def sendDataFile ( )

This sends a file to the EyeLink tracker PC. Source file name and destination file name should be given. Using this
function, an image or video can be uploaded from the Display PC to the Tracker PC. The image can later be used
as a Gaze Cursor Backdrop via a call to SendCommand("draw_image imagename.ext")

Remarks

This function is equivalent to the C API
int send_data_file(char *src, char *dest, int is_path=0)

Parameters

src Name of local file (including extension).
dest Short Name of eye tracker file to write to (including extension).

Returns

Size of file if successful, Otherwise Runtime Exception is raised.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 93

Example:
getEYELINK().sendDataFile("ur.jpg", "ur.jpg") # transfer images as files
getEYELINK().sendDataFile("small.wmv","small.wmv") # transfer images as files
getEYELINK().sendCommand("draw_image ur.jpg") # draw_image <name>
getEYELINK().sendCommand("draw_video small.wmv") # draw_video <name> <-- webui only command

See also

bitmapSaveAndBackdrop(), bitmapSave(), bitmapBackdrop(), sendCommand()

********4 getTrackerVersionString()

def getTrackerVersionString ( )

After connection, determines if the connected tracker is an EyeLink I or II (use getTrackerVersion) to get number
value.

Remarks

This is equivalent to the C API
INT16 eyelink_get_tracker_version(char *c);

Returns

A string indicating EyeLink tracker version.

********5 escapePressed()

def escapePressed ( )

This function tests if the 'ESC' key is held down, and is usually used to break out of nested loops.

Remarks

Under Windows XP, this call will not work in realtime mode at all, and will take several seconds to respond if
graphics are being drawn continuously. This function works well in realtime mode under Windows 2000. This
function is equivalent to the C API
INT16 escape_pressed(void);

Returns

1 if 'ESC' key held down 0 if not.

© SR Research Ltd. 2003-2024



94 Class Documentation

********6 pumpMessages()

def pumpMessages ( )

Forces the graphical environment to process any pending key or mouse events.

Remarks

This function is equivalent to the C API
INT16 message_pump(HWND dialog_hook);

********7 getSampleDataFlags()

def getSampleDataFlags ( )

After calling waitForBlockStart(), or after at least one sample or eye event has been read, returns sample
data content flag (0 if not in sample block).

Remarks

This function is equivalent to the C API
INT16 eyelink_sample_data_flags(void);

Returns

Possible return values are a set of the following bit flags:

Constant Name Value Description
SAMPLE_LEFT 0x8000 Data for left eye
SAMPLE_RIGHT 0x4000 Data for right eye
SAMPLE_TIMESTAMP 0x2000 always for link, used to compress files
SAMPLE_PUPILXY 0x1000 pupil x,y pair
SAMPLE_HREFXY 0x0800 head-referenced x,y pair
SAMPLE_GAZEXY 0x0400 gaze x,y pair
SAMPLE_GAZERES 0x0200 gaze res (x,y pixels per degree) pair
SAMPLE_PUPILSIZE 0x0100 pupil size
SAMPLE_STATUS 0x0080 error flags
SAMPLE_INPUTS 0x0040 input data port
SAMPLE_BUTTONS 0x0020 button state: LSBy state, MSBy changes
SAMPLE_HEADPOS 0x0010 head-position: byte tells # words
SAMPLE_TAGGED 0x0008 reserved variable-length tagged
SAMPLE_UTAGGED 0x0004 user-definable variable-length tagged

********8 stopRecording()

def stopRecording ( )

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 95

Stops recording, resets EyeLink data mode. Call 50 to 100 msec after an event occurs that ends the trial. This
function waits for mode switch before returning.

Remarks

This is equivalent to the C API
void stop_recording(void);

********9 getButtonStates()

def getButtonStates ( )

Returns a flag word with bits set to indicate which tracker buttons are currently pressed. This is button 1 for the
LSB, up to button 16 for the MSB. Buttons above 8 are not realized on the EyeLink tracker.

Remarks

This function is equivalent to the C API
UINT16 eyelink_button_states(void);

Returns

Flag bits for buttons currently pressed.

********0 abort()

def abort ( )

Places EyeLink tracker in off-line (idle) mode.

Remarks

Use before attempting to draw graphics on the tracker display, transferring files, or closing link. Always call
waitForModeReady() afterwards to ensure tracker has finished the mode transition. This function pair is
implemented by the EyeLink toolkit library function setOfflineMode(). This function is equivalent to the
C API
INT16 eyelink_abort(void);

Returns

0 if mode switch begun, else link error.

© SR Research Ltd. 2003-2024



96 Class Documentation

********1 stopPlayBack()

def stopPlayBack ( )

Stops playback if in progress. Flushes any data in queue.

Remarks

This function is equivalent to the C API
INT16 eyelink_playback_stop(void);

********2 sendTimedCommand()

def sendTimedCommand ( )

Sends a command to the connected eye tracker, wait for reply.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 97

Exceptions

If there is an error, Runtime exception is raised.

See also

sendTimedCommandEx()

********* getLastButtonPress()

def getLastButtonPress ( )

Reads the number of the last button detected by the EyeLink tracker. This is 0 if no buttons were pressed since the
last call, or since the buttons were flushed. If a pointer to a variable is supplied the eye-tracker timestamp of the
button may be read. This could be used to see if a new button has been pressed since the last read. If multiple
buttons were pressed since the last call, only the last button is reported.

Remarks

This function is equivalent to the C API
UINT16 eyelink_last_button_press(UINT32 *time);

Returns

Two-item tuple, recording the button last pressed (0 if no button pressed since last read) and the time of the
button press.

********* getkeyEx()

def getkeyEx ( )

Returns the key pressed. Same as getkey() except, this returns a tuple with the first value contains the key and the
second contains value contains the modifier.

Remarks

Warning: This function processes and dispatches any waiting messages. This will allow Windows to perform
disk access and negates the purpose of realtime mode. Usually these delays will be only a few millisec-
onds, but delays over 20 milliseconds have been observed. You may wish to call escapePressed() or
breakPressed() in recording loops instead of getkey() if timing is critical, for example in a gaze-
contingent display. Under Windows XP, these calls will not work in realtime mode at all (although these do
work under Windows 2000). Under Windows 95/98/Me, realtime performance is impossible even with this
strategy. Some useful keys are:

• CURS_UP
• CURS_DOWN
• CURS_LEFT
• CURS_RIGHT
• ESC_KEY
• ENTER_KEY
• TERMINATE_KEY
• JUNK_KEY

This function is equivalent to the C API
unsigned getkey(void);

© SR Research Ltd. 2003-2024



98 Class Documentation

Returns

0 if no key pressed, else key code. TERMINATE_KEY if CTRL-C held down or program has been terminated.

********* isInDataBlock()

def isInDataBlock ( )

Checks to see if framing events read from queue indicate that the data is in a block containing samples, events, or
both.

Remarks

The first item in queue may not be a block start even, so this should be used in a loop while discarding items
using eyelink_get_next_data(NULL). NOTE: this function did not work reliably in versions of the
DLL before v2.0 (did not detect end of blocks). This function is equivalent to the C API
INT16 eyelink_in_data_block(INT16 samples, INT16 events);

Parameters

samples if non-zero, check if in a block with samples.
events if non-zero, check if in a block with events.

Returns

0 if no data of either masked type is being sent.

********* nodeRequestTime()

def nodeRequestTime ( )

Sends a request the connected eye tracker to return its current time.

Remarks

The time reply can be read with getTrackerTime(). This function is equivalent to the C API
UINT32 eyelink_node_request_time(ELINKADDR node);

Parameters

address Text IP address (for example, "*********") for a specific tracker.

Returns

0 if no error, else link error code.

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 99

********* applyDriftCorrect()

def applyDriftCorrect ( )

Applies the results of the last drift correction. This is not done automatically after a drift correction, allowing the
message returned by getCalibrationMessage() to be examined first.

Remarks

This function is equivalent to the C API
INT16 eyelink_apply_driftcorr(void);

Returns

0 if command sent fine, else link error.

********* setAddress()

def setAddress ( )

Sets the IP address used for connection to the EyeLink tracker. This is set to "*********" in the DLL, but may need
to be changed for some network configurations. This must be set before attempting to open a connection to the
tracker.

A "broadcast" address ("***************") may be used if the tracker address is not known - this will work only if a
single Ethernet card is installed, or if DLL version 2.1 or higher, and the latest tracker software versions (EyeLink I
v2.1 or higher, and EyeLink II v1.1 or higher) are installed.

Remarks

This is equivalent to the C API
INT16 set_eyelink_address(char *addr);

Parameters

text_IP_address Pointer to a string containing a "dotted" 4-digit IP address;

Returns

0 if success, -1 if could not parse address string.

********9 readTime()

def readTime ( )

Returns the tracker time requested by eyelink_request_time() or eyelink_node_request_←↩

time().

© SR Research Ltd. 2003-2024



100 Class Documentation

Returns

0 if no response yet, else timestamp in millisecond.

See also

trackerTime()

********* getImageCrossHairData()

def getImageCrossHairData ( )

********* bitmapSaveAndBackdrop()

def bitmapSaveAndBackdrop ( )

This function saves the entire bitmap as a .BMP, .JPG, .PNG, or .TIF file, and transfers the image to tracker as
backdrop for gaze cursors.

Parameters

iwidth Original image width.
iheight Original image height.
pixels Pixels of the image in one of two possible formats: pixel=[line1, line2, ... linen]

line=[pix1,pix2,...,pixn],pix=(r,g,b). pixel=[line1, line2, ... linen]
line=[pix1,pix2,...,pixn],pix=0xAARRGGBB.

xs Crop x position.
ys Crop y position.
width Crop width.
height Crop height.
fname File name to save.
path Path to save.
svoptions Save options(SV_NOREPLACE, SV_MAKEPATH). If the file exists, it replaces the file unless

SV_NOREPLACE is specified.
xd X position - transfer.
yd Y position - transfer.
xferoptions Transfer options(BX_AVERAGE,BX_DARKEN,BX_LIGHTEN,BX_MAXCONTRAST,BX_N←↩

ODITHER,BX_GRAYSCALE). Transfer options set with bitwise OR of the following constants,
determines how bitmap is processed:

• BX_AVERAGE Averaging combined pixels

• BX_DARKEN Choosing darkest and keep thin dark lines.

• BX_LIGHTEN Choosing darkest and keep thin white lines and control how bitmap size is
reduced to fit tracker display.

• BX_MAXCONTRAST Maximizes contrast for clearest image.

• BX_NODITHER Disables the dithering of the image.
© SR Research Ltd. 2003-2024

• BX_GREYSCALE Converts the image to grayscale (grayscale works best for EyeLink I,
text, etc.).



4.10 EyeLinkCBind Class Reference 101

See also

bitmapBackdrop(),bitmapSave()

********* getEventTypeFlags()

def getEventTypeFlags ( )

After at least one button or eye event has been read, can be used to check what type of events will be available.

Remarks

This is equivalent to the C API
UINT16 eyelink_event_type_flags(void);

Returns

Possible return values are a set of the following bit flags:

Constant Name Value Description
LEFTEYE_EVENTS 0x8000 Has left eye events
RIGHTEYE_EVENTS 0x4000 Has right eye events
BLINK_EVENTS 0x2000 Has blink events
FIXATION_EVENTS 0x1000 Has fixation events
FIXUPDATE_EVENTS 0x0800 Has fixation updates
SACCADE_EVENTS 0x0400 Has saccade events
MESSAGE_EVENTS 0x0200 Has message events
BUTTON_EVENTS 0x0040 Has button events
INPUT_EVENTS 0x0020 Has input port events

********* trackerTimeOffset()

def trackerTimeOffset ( )

Returns the time difference between the tracker time and display pc time.

Remarks

This is equivalent to the C API
UINT32 eyelink_time_offset();

Returns

An integer data for the time difference (in milliseconds) between the tracker time and display pc time.

© SR Research Ltd. 2003-2024



102 Class Documentation

********* getPositionScalar()

def getPositionScalar ( )

Returns the divisor used to convert integer eye data to floating point data.

Remarks

This function is equivalent to the C API
INT16 eyelink_position_prescaler(void);

Returns

Integer for the divisor (usually 10).

********* waitForModeReady()

def waitForModeReady ( )

After a mode-change command is given to the EyeLink tracker, an additional 5 to 30 milliseconds may be needed
to complete mode setup. Call this function after mode change functions.

Remarks

If it does not return 0, assume a tracker error has occurred. This function is equivalent to the C API
INT16 eyelink_wait_for_mode_ready(UINT32 maxwait);

Parameters

maxwait Maximum milliseconds to wait for the mode to change.

Returns

0 if mode switching is done, else still waiting.

********* exitCalibration()

def exitCalibration ( )

This function should be called from a message or event handler if an ongoing call to doDriftCorrect() or
doTrackerSetup() should return immediately.

Remarks

This function is equivalent to the C API
void exit_calibration(void);

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 103

********* openNode()

def openNode ( )

Allows the computer to connect to tracker, where the tracker is on the same network.

Remarks

This is equivalent to the C API
INT16 eyelink_open_node(ELINKADDR node, INT16 busytest);

with node parameter converted from text to ELINKADDR.

Parameters

eyelink_address Text IP address of the host PC (the default value is, "*********").
busytest If non-zero the call to openNode() will not disconnect an existing connection.

Returns

Throws Runtime Exception if it connects to the remote host.

********* flushKeybuttons()

def flushKeybuttons ( )

Causes the EyeLink tracker and the EyeLink library to flush any stored button or key events. This should be used
before a trial to get rid of old button responses. The <enable_buttons> argument controls whether the
EyeLink library will store button press and release events. It always stores tracker key events. Even if disabled, the
last button pressed and button flag bits are updated.

Remarks

This is equivalent to the C API
INT16 eyelink_flush_keybuttons(INT16 enable_buttons);

Parameters

enable_buttons Sets to 0 to monitor last button press only, 1 to queue button events.

Returns

Always 0.

********* commandResult()

def commandResult ( )

© SR Research Ltd. 2003-2024



104 Class Documentation

Check for and retrieves the numeric result code sent by the tracker from the last command.

Remarks

This function is equivalent to the C API
INT16 eyelink_command_result(void);

Returns

NO_REPLY if no reply to last command.
OK_RESULT (0) if OK.
Other error codes represent tracker execution error.

********00 getFloatData()

def getFloatData ( )

Reads data of a specific type returned by getNextData(). If this function called multiple times without calling
getNextData(), the same data is returned.

Remarks

This function is equivalent to the C API
INT16 eyelink_get_next_data(void *buf);

Returns

None if no data available. Otherwise, a valid data is returned. The returned data type can be:

• Sample

• StartBlinkEvent

• EndBlinkEvent

• StartSacadeEvent

• EndSacadeEvent

• StartFixationEvent

• EndFixationEvent

• FixUpdateEvent

• IOEvent

• MessageEvent

© SR Research Ltd. 2003-2024



4.10 EyeLinkCBind Class Reference 105

********01 getTargetPositionAndState()

def getTargetPositionAndState ( )

Returns the current target position and state.

Remarks

This function is equivalent to the C API
INT16 eyelink_target_check(INT16 *x, INT16 *y);

Returns

A three-item tuple holding (in the following order):

• the target visibility (1 if visible, 0 if not),
• x position of the target,
• and y position of the target.

********02 startDriftCorrect()

def startDriftCorrect ( )

Sets the position of the drift correction target, and switches the tracker to drift-correction mode. Should be followed
by a call to waitForModeReady() method.

Remarks

This function is equivalent to the C API
INT16 eyelink_driftcorr_start(INT16 x, INT16 y);

Parameters

x x position of the target.
y y position of the target.

Returns

0 if command sent fine, else link error.

********03 trackerTime()

def trackerTime ( )

Returns the current tracker time (in milliseconds) since the tracker application started.

© SR Research Ltd. 2003-2024



106 Class Documentation

Remarks

This is equivalent to the C API
UINT32 eyelink_tracker_time();

Returns

An integer data for the current tracker time (in milliseconds) since tracker initialization.

4.11 EyeLinkCustomDisplay Class Reference

EyeLinkCustomDisplay is an abstract class, that one would implement to present calibration/validation/drift correc-
tion targets and camera images.

Public Member Functions

• def __init__ (self)
Constructor takes no parameters.

• def __updateimgsize__ (self, width, height)
Internal function to update the mage size.

• def setup_cal_display (self)
This function is called to setup calibration/validation display.

• def exit_cal_display (self)
This function is called just before exiting calibration/validation display.

• def record_abort_hide (self)
This function is called if abort of record.

• def setup_image_display (self, width, height)
This function is used to setup image display.

• def image_title (self, title)
This function displays the camera image title.

• def draw_image_line (self, width, line, totlines, buff)
This function is used to display an image line.

• def set_image_palette (self, red, green, blue)
This function is called to setup the image palettes.

• def exit_image_display (self)
Called to end the image display.

• def clear_cal_display (self)
Called to clear the calibration display.

• def erase_cal_target (self)
Called to erase the calibration or validation target.

• def draw_cal_target (self, x, y)
Called to draw the calibration or validation target.

• def play_beep (self, beepid)
Called to play target beeps.

• def get_input_key (self)
This function should return list of KeyInput.

• def alert_printf (self, msg)
Called to notify any error message to display or print.

• def draw_line (self, x1, y1, x2, y2, colorindex)
Called to draw the cross hair, in response to the call to draw_cross_hair().

© SR Research Ltd. 2003-2024



4.11 EyeLinkCustomDisplay Class Reference 107

• def draw_lozenge (self, x, y, width, height, colorindex)
Called to draw the cross hair, in response to the call to draw_cross_hair().

• def get_mouse_state (self)
Called to get the mouse location.

• def draw_cross_hair (self)
User call this function to request draw cross hair.

4.11.1 Detailed Description

EyeLinkCustomDisplay is an abstract class, that one would implement to present calibration/validation/drift correc-
tion targets and camera images.

In addition, EyeLinkCustomDisplay can also play target beeps and display error messages.

To use custom display do the following.

• 1. Implement EyeLinkCustomDisplay

• 2. Create an instance of the custom display object

• 3. Use pylink.openGraphicsEx to let pylink know to use the custom display object

Example:
genv = EyeLinkCoreGraphicsPyGame(800,600,eyelinktracker)
openGraphicsEx(genv)

Example implementation of EyeLinkCustomDisplay Code to implement EyeLinkCustomDisplay using pygame.

4.11.2 Member Function Documentation

******** __updateimgsize__()

def __updateimgsize__ (
self,
width,
height )

Internal function to update the mage size.

The size set by this function is used to draw cross hair when draw_cross_hair() is called This function should
not be overridden and should not be called other than the display mechanism.

Parameters

width Width of the image.
height Height of the image.

© SR Research Ltd. 2003-2024



108 Class Documentation

******** setup_cal_display()

def setup_cal_display (
self )

This function is called to setup calibration/validation display.

This will be called just before we enter into the calibration or validation or drift correction mode. Any allocation per
calibration or validation drift correction can be done here. Also, it is normal to clear the display in this call.

******** exit_cal_display()

def exit_cal_display (
self )

This function is called just before exiting calibration/validation display.

Any resource allocation done in setup_cal_display() can be cleared.

******** record_abort_hide()

def record_abort_hide (
self )

This function is called if abort of record.

It is used to hide display from subject.

******** setup_image_display()

def setup_image_display (
self,
width,
height )

This function is used to setup image display.

It takes expected image size of the source image. This may be called repeatedly for same display. If this fails, It
should return 1 if success and 0 otherwise.

Parameters

width Width of the incoming image.
height Height of the incoming image.

Returns

1 if success, 0 otherwise.

© SR Research Ltd. 2003-2024



4.11 EyeLinkCustomDisplay Class Reference 109

******** image_title()

def image_title (
self,
title )

This function displays the camera image title.

This is called whenever the title changes

Parameters

title title change

******** draw_image_line()

def draw_image_line (
self,
width,
line,
totlines,
buff )

This function is used to display an image line.

This function is called with an array of bytes containing picture colors. The byte on pixels are just palette indexes.
This index should be used against the palette created on the call to set_image_palette_hook(). The image
is given line by line from top to bottom. It may be efficient to collect one full image and do a full blit of the entire
image.
i =0
imline = self.imagebuffer[line-1]
while i <width:

imline[i] = self.pal[buff[i]]
i= i+1

******** set_image_palette()

def set_image_palette (
self,
red,
green,
blue )

This function is called to setup the image palettes.

The function is called with a set of RGB colors to set up for next image.
self.pal = []
while i < sz:

rf = int(b[i])
gf = int(g[i])
bf = int(r[i])
self.pal.append((rf«16) | (gf«8) | (bf))
i = i+1

© SR Research Ltd. 2003-2024



110 Class Documentation

******** erase_cal_target()

def erase_cal_target (
self )

Called to erase the calibration or validation target.

Erase the target drawn by the previous call to draw_cal_target().

********0 draw_cal_target()

def draw_cal_target (
self,
x,
y )

Called to draw the calibration or validation target.

Draw a target at x,y and display it.

Remarks

x and y values are relative to the active screen_pixel_coords command.

Parameters

x X location to draw the target.
y Y location to draw the target.

********1 play_beep()

def play_beep (
self,
beepid )

Called to play target beeps.

© SR Research Ltd. 2003-2024



4.11 EyeLinkCustomDisplay Class Reference 111

Parameters

beepid Id of the beep to be played. Possible values for beepid
are:

• CAL_ERR_BEEP =-1

• DC_ERR_BEEP =-2

• CAL_GOOD_BEEP = 0

• CAL_TARG_BEEP = 1

• DC_GOOD_BEEP = 2

• DC_TARG_BEEP = 3

********2 get_input_key()

def get_input_key (
self )

This function should return list of KeyInput.

If there are not keys, return an empty list or None.

********3 draw_line()

def draw_line (
self,
x1,
y1,
x2,
y2,
colorindex )

Called to draw the cross hair, in response to the call to draw_cross_hair().

This function should draw a line from (x1,y1) to (x2,y2). The x and y values are relative to the width and height of
the image, given at setup_image_display().

Parameters

x1 Starting x position.
y1 Starting y position.
x2 Ending x position.
y2 Ending y position.
colorindex Color id of the line. Possible value for colorindex

are:

• CR_HAIR_COLOR=1

• PUPIL_HAIR_COLOR=2

© SR Research Ltd. 2003-•202P4UPIL_BOX_COLOR=3

• SEARCH_LIMIT_BOX_COLOR=4

• MOUSE_CURSOR_COLOR=5



112 Class Documentation

********4 draw_lozenge()

def draw_lozenge (
self,
x,
y,
width,
height,
colorindex )

Called to draw the cross hair, in response to the call to draw_cross_hair().

This function should draw an lozenge bounded by the box (x,y),(width,height). The x and y values are relative to the
width and height of the image, given at setup_image_display().

Remarks

This function is not used at the moment.

Parameters

x Starting x position.
y Starting y position.
width bounding width
height bounding height
colorindex Color id of the ellipse. Possible value for colorindex

are:

• CR_HAIR_COLOR=1

• PUPIL_HAIR_COLOR=2

• PUPIL_BOX_COLOR=3

• SEARCH_LIMIT_BOX_COLOR=4

• MOUSE_CURSOR_COLOR=5

********5 get_mouse_state()

def get_mouse_state (
self )

Called to get the mouse location.

This function should return the mouse location and the state at the time of call. ((x,y),state). At the moment we
only care if the mouse is clicked or not. So, if clicked the state = 1, 0 otherwise. This function is only useful for
EyeLink1000.

© SR Research Ltd. 2003-2024



4.12 EyeLinkListener Class Reference 113

********6 draw_cross_hair()

def draw_cross_hair (
self )

User call this function to request draw cross hair.

After completion of filling in the camera image, call this function to draw the cross hair on the camera image.

4.12 EyeLinkListener Class Reference

EyeLinkListener class implements most of the core EyeLink interface.

Inherits EyeLinkCBind.

Inherited by EyeLink.

Public Member Functions
• def getTrackerInfo (self)

Returns the current tracker information.
• def drawCalTarget (self, position)

Allow the normal calibration target drawing to proceed at different locations.
• def getCurrentTime (self)

returns the current tracker time.
• def getSampleRate (self)

returns the current sample rate.
• def getCRMode (self)

returns the current mode data, either PUPIL_ONLY or PUPIL_CR.
• def getLinkFilter (self)

returns the Link Filter Level.
• def getFileFilter (self)

returns the File Filter Level.
• def getEyeUsed (self)

returns the eye used.
• def sendMessage (self, message_text, offset=0)

Sends the given message to the connected EyeLink tracker.
• def imageBackdrop (self, filename, Xs, Ys, width, height, Xd, Yd, xferoptions)

Sends the given image file backdrop to the connected EyeLink tracker.

4.12.1 Detailed Description

EyeLinkListener class implements most of the core EyeLink interface.

This includes the simple connection to the eye tracker, sending commands and messages to the tracker, opening
and saving a recording file, performing calibration and drift correction, real-time access to tracker data and eye
movement events (such as fixations, blinks, and saccades), as well as other important operations.

An instance of the EyeLinkListener class can be created by using the class constructor function. For example,
try:

EYELINK = EyeLinkListener()
except:

EYELINK = None

All of the methods should be called in the format of: EYELINK.functionName(parameters), where EYELINK is an
instance of the EyeLinkListener class.

© SR Research Ltd. 2003-2024



114 Class Documentation

4.12.2 Member Function Documentation

******** getTrackerInfo()

def getTrackerInfo (
self )

Returns the current tracker information.

Returns

An instance of the ILinkData class.

******** drawCalTarget()

def drawCalTarget (
self,
position )

Allow the normal calibration target drawing to proceed at different locations.

This is equivalent to the C API
INT16 CALLTYPE set_draw_cal_target_hook(INT16 (CALLBACK * erase_cal_target_hook)(HDC hdc), INT16 options);

Parameters

position A tuple in the format of (x, y), passing along the position of drift correction target. X and y are in
screen pixels.

******** sendMessage()

def sendMessage (
self,
message_text,
offset = 0 )

Sends the given message to the connected EyeLink tracker.

The message will be written to the EyeLink tracker.

Remarks

This is equivalent to the C API
int eyecmd_printf(char *fmt, ...);

The maximum text length is 130 characters. If the given string has more than 130 characters, the first 130
characters will be sent and if the send passes this function will return 1. If the text is not truncated, 0 will be
returned on a successful message send.

© SR Research Ltd. 2003-2024



4.12 EyeLinkListener Class Reference 115

Parameters

message_text text message to be sent. It does not support printf() kind of formatting.
offset time offset in millisencond for the message.

Returns

If there is any problem sending the message, a runtime exception is raised.

******** imageBackdrop()

def imageBackdrop (
self,
filename,
Xs,
Ys,
width,
height,
Xd,
Yd,
xferoptions )

Sends the given image file backdrop to the connected EyeLink tracker.

Remarks

This will open the image file, convert to bitmap using PIL.Image and call bitmapBackdrop to send the image
to host

Parameters

filename - full or relative path of the image file name
Xs - crop x position
Ys - crop y position
width - crop width
height - crop height
Xd - xposition - transfer
Yd - yposition - transfer
xferoptions - transfer options(BX_AVERAGE,BX_DARKEN,BX_LIGHTEN,BX_MAXCONTRAST,BX_NODI←↩

THER,BX_GRAYSCALE)

© SR Research Ltd. 2003-2024



116 Class Documentation

Returns

if PIL couldn't load, it will return None, otherwise return value of bitmapBackdrop

4.13 EyelinkMessage Class Reference

EyelinkMessage class, derived from EyeLinkAddress class, is used to send and receive messages between EyeLink
nodes.

Inherits EyeLinkAddress.

Public Member Functions

• def __init__ (self, ip=(100, 1, 1, 1), port=4000, msg="")
Constructor.

• def getText ()
Returns the message to be sent to or received from the node.

4.13.1 Detailed Description

EyelinkMessage class, derived from EyeLinkAddress class, is used to send and receive messages between EyeLink
nodes.

Instances of this class are commonly used as the return values of the nodeReceive() and getNode() meth-
ods of the EyeLinkListener or EyeLink class. An instance of EyelinkMessage class can be initialized with the class
constructor:
EyelinkMessage(ip = (100,1,1,1), port = 4000, msg = "")

where ip is a four-item tuple containing the IP address of the EyeLink node, port is the port number of the connection,
msg is the message to be sent to or received from the node.

For example,
myMessage = EyelinkMessage((100, 1, 1, 1), 4000, "test")

4.13.2 Constructor & Destructor Documentation

******** __init__()

def __init__ (
self,
ip = (100,1,1,1),
port = 4000,
msg = "" )

Constructor.

© SR Research Ltd. 2003-2024



4.14 FixUpdateEvent Class Reference 117

Parameters

ip optional ipaddress in tuple form. eg. if the ip address is *************, the tuple form is (192,168,25,48)
The default ip address is *********

port optional port value as integer. The default value is 4000.
msg text message.

4.13.3 Member Function Documentation

******** getText()

def getText ( )

Returns the message to be sent to or received from the node.

Returns

Text message.

4.14 FixUpdateEvent Class Reference

Class to represent the Fix Update event.

Inherits StartNonBlinkEvent, and EndNonBlinkEvent.

Public Member Functions

• def getStartPupilSize (self)
Pupil size (in arbitrary units, area or diameter as selected) at the start of a fixation interval.

• def getAverageGaze (self)
The average gaze position during the fixation period (in pixel coordinates set by the screen_pixel_coords
command).

• def getAverageHREF (self)
Average HEADREF position during the fixation period.

• def getAveragePupilSize (self)
Average pupil size (in arbitrary units, area or diameter as selected) during a fixation.

• def getEndPupilSize (self)
Pupil size (in arbitrary units, area or diameter as selected) at the end of a fixation interval.

4.14.1 Detailed Description

Class to represent the Fix Update event.

This also inherits all properties from StartNonBlinkEvent and EndNonBlinkEvent.

© SR Research Ltd. 2003-2024



118 Class Documentation

4.14.2 Member Function Documentation

******** getStartPupilSize()

def getStartPupilSize (
self )

Pupil size (in arbitrary units, area or diameter as selected) at the start of a fixation interval.

Returns

Float.

******** getAverageGaze()

def getAverageGaze (
self )

The average gaze position during the fixation period (in pixel coordinates set by the screen_pixel_coords
command).

Returns

Two-item tuple in the format of (float, float).

******** getAverageHREF()

def getAverageHREF (
self )

Average HEADREF position during the fixation period.

Returns

Two-item tuple in the format of (float, float).

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 119

******** getAveragePupilSize()

def getAveragePupilSize (
self )

Average pupil size (in arbitrary units, area or diameter as selected) during a fixation.

Returns

Float.

******** getEndPupilSize()

def getEndPupilSize (
self )

Pupil size (in arbitrary units, area or diameter as selected) at the end of a fixation interval.

Returns

Float.

4.15 ILinkData Class Reference

Class to represent tracker status information such as time stamps, flags, tracker addresses and so on.

Public Member Functions

• def getTime (self)
Time of last control event.

• def getSampleRate (self)
10∗sample rate (0 if no samples, 1 if nonconstant).

• def getSampleDivisor (self)
Sample "divisor" (min msec between samples).

• def getPrescaler (self)
Amount to divide gaze x,y,res by.

• def getVelocityPrescaler (self)
Amount to divide velocity by.

• def getPupilPrescaler (self)
Pupil prescale (1 if area, greater if diameter).

• def getHeadDistancePrescaler (self)
Head-distance prescale (to mm).

• def getSampleDataFlags (self)
0 if off, else all flags.

• def getEventDataFlags (self)
0 if off, else all flags.

• def getEventTypeFlags (self)

© SR Research Ltd. 2003-2024



120 Class Documentation

0 if off, else event-type flags.
• def isInBlockWithSamples (self)

Set if in block with samples.
• def isInBlockWithEvents (self)

Set if in block with events.
• def haveLeftEye (self)

Set if any left-eye data expected.
• def haveRightEye (self)

Set if any right-eye data expected.
• def getLostDataTypes (self)

Flags what we lost before last item.
• def getLastBufferType (self)

Buffer-type code.
• def getLastBufferSize (self)

Buffer size of last item.
• def isControlEvent (self)

Set if control event read with last data.
• def isNewBlock (self)

Set if control event started new block.
• def getLastItemTimeStamp (self)

Time field of item.
• def getLastItemType (self)

Type: 100 = sample, 0 = none, else event type.
• def getLastItemContent (self)

Content: <read> (IEVENT), <flags> (ISAMPLE).
• def getBlockNumber (self)

Block in file.
• def getSamplesInBlock (self)

Samples read in block so far.
• def getEventsInBlock (self)

Events (excl.
• def getLastResX (self)

Updated by samples only.
• def getLastResY (self)

Updated by samples only.
• def getLastPupil (self)

Updated by samples only.
• def getLastItemStatus (self)

Updated by samples, events.
• def getSampleQueueLength (self)

Number of items in queue.
• def getEventQueueLength (self)

Includes control events.
• def getQueueSize (self)

Total queue buffer size.
• def getFreeQueueLength (self)

Unused bytes in queue.
• def getLastReceiveTime (self)

Time tracker last sent packet.
• def isSamplesEnabled (self)

Data type rcve enable (switch).

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 121

• def isEventsEnabled (self)
Data type rcve enable (switch).

• def getPacketFlags (self)
Status flags from data packet.

• def getLinkFlags (self)
Status flags from link packet header.

• def getStateFlags (self)
Tracker error state flags.

• def getTrackerDataOutputState (self)
Tracker data output state.

• def getPendingCommands (self)
Tracker commands pending.

• def isPoolingRemote (self)
1 if polling remotes, else polling trackers.

• def getPoolResponse (self)
Total nodes responding to polling.

• def getReserved (self)
0 for EyeLink I or original EyeLink API DLL.

• def getName (self)
A name for our machine.

• def getTrackerName (self)
Name of tracker connected to.

• def getNodes (self)
Data on nodes.

• def getLastItem (self)
Buffer containing last item.

• def getAddress (self)
Address of our machine.

• def getTrackerAddress (self)
Address of the connected tracker.

• def getTrackerBroadcastAddress (self)
Broadcast address for eye trackers.

• def getRemoteBroadcastAddress (self)
Broadcast address for remotes Equivalent field in ILINKDATA "C": rbroadcast_address.

4.15.1 Detailed Description

Class to represent tracker status information such as time stamps, flags, tracker addresses and so on.

A valid reference to this object can be obtained by calling the function getEYELINK().getTrackerInfo().

4.15.2 Member Function Documentation

© SR Research Ltd. 2003-2024



122 Class Documentation

******** getTime()

def getTime (
self )

Time of last control event.

Equivalent field in ILINKDATA "C": Time.

******** getSampleRate()

def getSampleRate (
self )

10∗sample rate (0 if no samples, 1 if nonconstant).

Equivalent field in ILINKDATA "C": samrate.

******** getSampleDivisor()

def getSampleDivisor (
self )

Sample "divisor" (min msec between samples).

Equivalent field in ILINKDATA "C": samdiv.

******** getPrescaler()

def getPrescaler (
self )

Amount to divide gaze x,y,res by.

Equivalent field in ILINKDATA "C": prescaler.

******** getVelocityPrescaler()

def getVelocityPrescaler (
self )

Amount to divide velocity by.

Equivalent field in ILINKDATA "C": vprescaler.

******** getPupilPrescaler()

def getPupilPrescaler (
self )

Pupil prescale (1 if area, greater if diameter).

Equivalent field in ILINKDATA "C": pprescaler.

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 123

******** getHeadDistancePrescaler()

def getHeadDistancePrescaler (
self )

Head-distance prescale (to mm).

Equivalent field in ILINKDATA "C": hprescaler.

******** getSampleDataFlags()

def getSampleDataFlags (
self )

0 if off, else all flags.

Equivalent field in ILINKDATA "C": sample_data.

******** getEventDataFlags()

def getEventDataFlags (
self )

0 if off, else all flags.

Equivalent field in ILINKDATA "C": event_data.

********0 getEventTypeFlags()

def getEventTypeFlags (
self )

0 if off, else event-type flags.

Equivalent field in ILINKDATA "C": event_types.

********1 isInBlockWithSamples()

def isInBlockWithSamples (
self )

Set if in block with samples.

Equivalent field in ILINKDATA "C": in_sample_block.

********2 isInBlockWithEvents()

def isInBlockWithEvents (
self )

Set if in block with events.

Equivalent field in ILINKDATA "C": in_event_block.

© SR Research Ltd. 2003-2024



124 Class Documentation

********3 haveLeftEye()

def haveLeftEye (
self )

Set if any left-eye data expected.

Equivalent field in ILINKDATA "C": have_left_eye.

********4 haveRightEye()

def haveRightEye (
self )

Set if any right-eye data expected.

Equivalent field in ILINKDATA "C": have_right_eye.

********5 getLostDataTypes()

def getLostDataTypes (
self )

Flags what we lost before last item.

Equivalent field in ILINKDATA "C": last_data_gap_types.

********6 getLastBufferType()

def getLastBufferType (
self )

Buffer-type code.

Equivalent field in ILINKDATA "C": last_data_buffer_type.

********7 getLastBufferSize()

def getLastBufferSize (
self )

Buffer size of last item.

Equivalent field in ILINKDATA "C": last_data_buffer_size.

********8 isControlEvent()

def isControlEvent (
self )

Set if control event read with last data.

Equivalent field in ILINKDATA "C": control_read.

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 125

********9 isNewBlock()

def isNewBlock (
self )

Set if control event started new block.

Equivalent field in ILINKDATA "C": first_in_block.

********0 getLastItemTimeStamp()

def getLastItemTimeStamp (
self )

Time field of item.

Equivalent field in ILINKDATA "C": last_data_item_time.

********1 getLastItemType()

def getLastItemType (
self )

Type: 100 = sample, 0 = none, else event type.

Equivalent field in ILINKDATA "C": last_data_item_type.

********2 getLastItemContent()

def getLastItemContent (
self )

Content: <read> (IEVENT), <flags> (ISAMPLE).

Equivalent field in ILINKDATA "C": last_data_item_contents.

********3 getBlockNumber()

def getBlockNumber (
self )

Block in file.

Equivalent field in ILINKDATA "C": block_number.

********4 getSamplesInBlock()

def getSamplesInBlock (
self )

Samples read in block so far.

Equivalent field in ILINKDATA "C": block_sample.

© SR Research Ltd. 2003-2024



126 Class Documentation

********5 getEventsInBlock()

def getEventsInBlock (
self )

Events (excl.

control read in block). Equivalent field in ILINKDATA "C": block_event.

********6 getLastResX()

def getLastResX (
self )

Updated by samples only.

Equivalent field in ILINKDATA "C": last_resx.

********7 getLastResY()

def getLastResY (
self )

Updated by samples only.

Equivalent field in ILINKDATA "C": last_resy.

********8 getLastPupil()

def getLastPupil (
self )

Updated by samples only.

Equivalent field in ILINKDATA "C": last_pupil.

********9 getLastItemStatus()

def getLastItemStatus (
self )

Updated by samples, events.

Equivalent field in ILINKDATA "C": last_status.

********0 getSampleQueueLength()

def getSampleQueueLength (
self )

Number of items in queue.

Equivalent field in ILINKDATA "C": queued_samples.

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 127

********1 getEventQueueLength()

def getEventQueueLength (
self )

Includes control events.

Equivalent field in ILINKDATA "C": queued_events.

********2 getQueueSize()

def getQueueSize (
self )

Total queue buffer size.

Equivalent field in ILINKDATA "C": queue_size.

********3 getFreeQueueLength()

def getFreeQueueLength (
self )

Unused bytes in queue.

Equivalent field in ILINKDATA "C": queue_free.

********4 getLastReceiveTime()

def getLastReceiveTime (
self )

Time tracker last sent packet.

Equivalent field in ILINKDATA "C": last_rcve_time.

********5 isSamplesEnabled()

def isSamplesEnabled (
self )

Data type rcve enable (switch).

Equivalent field in ILINKDATA "C": samples_on.

********6 isEventsEnabled()

def isEventsEnabled (
self )

Data type rcve enable (switch).

Equivalent field in ILINKDATA "C": events_on.

© SR Research Ltd. 2003-2024



128 Class Documentation

********7 getPacketFlags()

def getPacketFlags (
self )

Status flags from data packet.

Equivalent field in ILINKDATA "C": packet_flags.

********8 getLinkFlags()

def getLinkFlags (
self )

Status flags from link packet header.

Equivalent field in ILINKDATA "C": link_flags.

********9 getStateFlags()

def getStateFlags (
self )

Tracker error state flags.

Equivalent field in ILINKDATA "C": state_flags.

********0 getTrackerDataOutputState()

def getTrackerDataOutputState (
self )

Tracker data output state.

Equivalent field in ILINKDATA "C": link_dstatus.

********1 getPendingCommands()

def getPendingCommands (
self )

Tracker commands pending.

Equivalent field in ILINKDATA "C": link_pendcmd.

********2 isPoolingRemote()

def isPoolingRemote (
self )

1 if polling remotes, else polling trackers.

Equivalent field in ILINKDATA "C": polling_remotes.

© SR Research Ltd. 2003-2024



4.15 ILinkData Class Reference 129

********3 getPoolResponse()

def getPoolResponse (
self )

Total nodes responding to polling.

Equivalent field in ILINKDATA "C": poll_responses.

********4 getReserved()

def getReserved (
self )

0 for EyeLink I or original EyeLink API DLL.

Equivalent field in ILINKDATA "C": reserved.

********5 getName()

def getName (
self )

A name for our machine.

Equivalent field in ILINKDATA "C": our_name.

********6 getTrackerName()

def getTrackerName (
self )

Name of tracker connected to.

Equivalent field in ILINKDATA "C": eye_name.

********7 getNodes()

def getNodes (
self )

Data on nodes.

Equivalent field in ILINKDATA "C": nodes.

********8 getLastItem()

def getLastItem (
self )

Buffer containing last item.

Equivalent field in ILINKDATA "C": last_data_item.

© SR Research Ltd. 2003-2024



130 Class Documentation

********9 getAddress()

def getAddress (
self )

Address of our machine.

Equivalent field in ILINKDATA "C": our_address.

********0 getTrackerAddress()

def getTrackerAddress (
self )

Address of the connected tracker.

Equivalent field in ILINKDATA "C": eye_address.

********1 getTrackerBroadcastAddress()

def getTrackerBroadcastAddress (
self )

Broadcast address for eye trackers.

Equivalent field in ILINKDATA "C": ebroadcast_address.

4.16 IOEvent Class Reference

IOEvent class is used to handle BUTTONEVENT and INPUTEVENT types, which report changes in button status
or in the input port data.

Inherited by ButtonEvent.

Public Member Functions

• def getTime (self)
Timestamp of the sample causing event (in milliseconds since EyeLink tracker was activated).

• def getType (self)
The event code.

• def getData (self)
Coded event data.

© SR Research Ltd. 2003-2024



4.16 IOEvent Class Reference 131

4.16.1 Detailed Description

IOEvent class is used to handle BUTTONEVENT and INPUTEVENT types, which report changes in button status
or in the input port data.

The getTime() method records the timestamp of the eye-data sample where the change occurred, although the
event itself is usually sent before that sample. Button events from the link are rarely used; monitoring buttons with
one of readKeybutton(), lastButtonPress(), or buttonStates() of the EyeLink class methods is
preferable, since these can report button states at any time, not just during recording.

Returned by getFloatData() whenever there is an IOEvent.

4.16.2 Member Function Documentation

******** getTime()

def getTime (
self )

Timestamp of the sample causing event (in milliseconds since EyeLink tracker was activated).

Returns

Long integer.

******** getType()

def getType (
self )

The event code.

This should be BUTTONEVENT (i.e., 25) or INPUTEVENT (i.e., 28).

Returns

Integer.

© SR Research Ltd. 2003-2024



132 Class Documentation

******** getData()

def getData (
self )

Coded event data.

Returns

Long integer.

4.17 KeyInput Class Reference

This represents a key input.

4.17.1 Detailed Description

This represents a key input.

This is used with EyeLinkCustomDisplay to notify the eyelink_core.dll that a key input is available.

4.18 MessageEvent Class Reference

A message event is created by your experiment program and placed in the EDF file.

Public Member Functions

• def getTime (self)
Timestamp of the sample causing event (when camera imaged eye, in milliseconds since EyeLink tracker was acti-
vated).

• def getType (self)
The event code.

• def getText (self)
Message contents (max length 255 characters).

4.18.1 Detailed Description

A message event is created by your experiment program and placed in the EDF file.

It is possible to enable the sending of these messages back through the link, although there is rarely a reason to do
this. The MessageEvent class is designed to hold information on EyeLink message events retrieved from the link.
Please note that all methods for the MessageEvent class do not take a parameter.

Returned by getFloatData() whenever there is a Message Event.

© SR Research Ltd. 2003-2024



4.19 Sample Class Reference 133

4.18.2 Member Function Documentation

******** getTime()

def getTime (
self )

Timestamp of the sample causing event (when camera imaged eye, in milliseconds since EyeLink tracker was
activated).

Returns

Long integer.

******** getType()

def getType (
self )

The event code.

This should be MESSAGEEVENT (i.e., 24).

Returns

Integer.

******** getText()

def getText (
self )

Message contents (max length 255 characters).

Returns

String.

4.19 Sample Class Reference

The EyeLink toolkit library defines special data classes that allow the same programming calls to be used on different
platforms such as Windows, Linux and macOS.

© SR Research Ltd. 2003-2024



134 Class Documentation

Public Member Functions

• def initFromSample (self, sample)
Convenient method to clone a sample.

• def isLeftSample (self)
1 if the sample contains the left eye data; 0 if not.

• def isRightSample (self)
1 if the sample contains the right eye data; 0 if not.

• def isBinocular (self)
1 if the sample contains data from both eyes; 0 if not.

• def getTime (self)
Timestamp when camera imaged eye (in milliseconds since EyeLink tracker was activated).

• def getType (self)
Always SAMPLE_TYPE.

• def getPPD (self)
Angular resolution at current gaze position in screen pixels per visual degree.

• def getStatus (self)
Error and status flags (only useful for EyeLink II and newer hardware, report CR status and tracking error).

• def getInput (self)
Data from input port(s).

• def getFlags (self)
Bits indicating what types of data are present, and for which eye(s).

• def getButtons (self)
Button input data: high 8 bits indicate changes from last sample, low 8 bits indicate current state of buttons 8 (MSB)
to 1 (LSB).

• def getRightEye (self)
Returns the sample data information from the desired eye.

• def getLeftEye (self)
Returns the sample data information from the desired eye.

• def getHData (self)
Returns the href data.

• def getEye (self)
Returns Eye data status.

• def getTargetDistance (self)
Target Distance.

• def getTargetX (self)
Target X.

• def getTargetY (self)
Target Y.

• def getTargetFlags (self)
Target Flags.

4.19.1 Detailed Description

The EyeLink toolkit library defines special data classes that allow the same programming calls to be used on different
platforms such as Windows, Linux and macOS.

You will need to know these classes to read the examples and to write your own experiments. In this documentation,
the common data classes are: Sample class, Eye Event Classes, MessageEvent Class, and IOEvent Class. You
only need to read this section if you are planning to use real-time link data for gaze-contingent displays or gaze-
controlled interfaces, or to use data playback.

© SR Research Ltd. 2003-2024



4.19 Sample Class Reference 135

The EyeLink tracker measures eye position 250 or 2000 times per second depending on the tracking hardware and
the tracker mode you are working with, and computes true gaze position on the display using the head camera data.
This data is stored in the EDF file, and made available through the link in as little as 3 milliseconds after a physical
eye movement.

Samples can be read from the link by getFloatData() or getNewestSample() method of the EyeLink/←↩

EyeLinkLisenter class. These functions can return instances of Sample class. For example,
newSample = getEYELINK().getFloatData()

The following methods can be used to retrieve properties of a Sample class instance. For example, the timestamp
of the sample can be retrieved as newSample.getTime(). Please note that all methods for the Sample class
do not take a parameter whereas the return values are noted.

4.19.2 Member Function Documentation

******** isLeftSample()

def isLeftSample (
self )

1 if the sample contains the left eye data; 0 if not.

Returns

Integer.

******** isRightSample()

def isRightSample (
self )

1 if the sample contains the right eye data; 0 if not.

Returns

Integer.

******** isBinocular()

def isBinocular (
self )

1 if the sample contains data from both eyes; 0 if not.

Returns

Integer.

© SR Research Ltd. 2003-2024



136 Class Documentation

******** getTime()

def getTime (
self )

Timestamp when camera imaged eye (in milliseconds since EyeLink tracker was activated).

Returns

Long integer.

******** getType()

def getType (
self )

Always SAMPLE_TYPE.

Returns

Integer.

******** getPPD()

def getPPD (
self )

Angular resolution at current gaze position in screen pixels per visual degree.

The first item of the tuple stores the x-coordinate resolution and the second item of the tuple stores the y-coordinate
resolution.

Returns

Two-item tuple in the format of (float, float).

******** getStatus()

def getStatus (
self )

Error and status flags (only useful for EyeLink II and newer hardware, report CR status and tracking error).

See eye_data.h for useful bits.

Returns

Integer.

© SR Research Ltd. 2003-2024



4.19 Sample Class Reference 137

******** getInput()

def getInput (
self )

Data from input port(s).

Returns

Integer.

4.19.2.9 getFlags()

def getFlags (
self )

Bits indicating what types of data are present, and for which eye(s).

See eye_data.h for useful bits.

Returns

Integer.

********0 getButtons()

def getButtons (
self )

Button input data: high 8 bits indicate changes from last sample, low 8 bits indicate current state of buttons 8 (MSB)
to 1 (LSB).

Returns

Integer.

********1 getRightEye()

def getRightEye (
self )

Returns the sample data information from the desired eye.

Returns

Instance of sample data class.

© SR Research Ltd. 2003-2024



138 Class Documentation

********2 getLeftEye()

def getLeftEye (
self )

Returns the sample data information from the desired eye.

Returns

Instance of sample data class.

********3 getEye()

def getEye (
self )

Returns Eye data status.

Returns

• 2 if both left and right eye data are present
• 1 if right eye data present
• 0 if left eye data present

********4 getTargetDistance()

def getTargetDistance (
self )

Target Distance.

Returns

Float.

********5 getTargetX()

def getTargetX (
self )

Target X.

Returns

Float.

© SR Research Ltd. 2003-2024



4.20 SampleData Class Reference 139

********6 getTargetY()

def getTargetY (
self )

Target Y.

Returns

Float.

********7 getTargetFlags()

def getTargetFlags (
self )

Target Flags.

Returns

Float.

4.20 SampleData Class Reference

Sample data for left and right eye.

Public Member Functions

• def getGaze (self)
Display gaze position (in pixel coordinates set by the screen_pixel_coords command).

• def getHREF (self)
HREF angular coordinates.

• def getRawPupil (self)
Camera x, y of pupil center.

• def getPupilSize (self)
Pupil size (in arbitrary units, area or diameter as selected).

© SR Research Ltd. 2003-2024



140 Class Documentation

4.20.1 Detailed Description

Sample data for left and right eye.

The getRightEye() or getLeftEye() functions returns an instance of SampleData class, which contains
the current sample position (raw, HREF, or gaze) and pupil size information of the desired eye. The following
methods can be used to retrieve the attributes of an instance of the SampleData class.

For example, the x gaze position of the left eye for a given sample can be retrieved as:
newSample = getEYELINK().getFloatData()
gaze = newSample.getLeftEye().getGaze()
left_eye_gaze_x = gaze[0]

If certain property information not sent for this sample, the value MISSING_DATA (or 0, depending on the field)
will be returned, and the corresponding bit in the flags field will be zero (see eye_data.h for a list of bits).
Data may be missing because of the tracker configuration (set by commands sent at the start of the experiment,
from the Set Options screen of the EyeLink II tracker and newer eye tracker models, or from the default configuration
set by the DATA.INI file for the EyeLink I tracker). Eye position data may also be set to MISSING_VALUE during
a blink.

4.20.2 Member Function Documentation

******** getGaze()

def getGaze (
self )

Display gaze position (in pixel coordinates set by the screen_pixel_coords command).

The first and second item of the tuple store the x- and y- coordinate gaze position respectively.

Returns

Two-item tuple in the format of (float, float).

******** getHREF()

def getHREF (
self )

HREF angular coordinates.

The first and second items of the tuple are for the x and y coordinates, respectively.

Returns

Two-item tuple in the format of (float, float).

© SR Research Ltd. 2003-2024



4.21 StartBlinkEvent Class Reference 141

******** getRawPupil()

def getRawPupil (
self )

Camera x, y of pupil center.

The first and second items of the tuple store pupil center in the x- and y- coordinate respectively.

Returns

Two-item tuple in the format of (float, float).

******** getPupilSize()

def getPupilSize (
self )

Pupil size (in arbitrary units, area or diameter as selected).

Returns

Float.

4.21 StartBlinkEvent Class Reference

Class to represent Start Blink event.

Inherits EyeEvent.

Additional Inherited Members

4.21.1 Detailed Description

Class to represent Start Blink event.

There are no direct properties for this interface. All properties are inherited from EyeEvent.

4.22 StartFixationEvent Class Reference

Class to represent Start Fixation event.

Inherits StartNonBlinkEvent.

Inherited by EndFixationEvent.

© SR Research Ltd. 2003-2024



142 Class Documentation

Public Member Functions

• def getStartPupilSize (self)
Pupil size (in arbitrary units, area or diameter as selected) at the start of a fixation interval.

4.22.1 Detailed Description

Class to represent Start Fixation event.

This also inherits all properties from StartNonBlinkEvent.

4.22.2 Member Function Documentation

******** getStartPupilSize()

def getStartPupilSize (
self )

Pupil size (in arbitrary units, area or diameter as selected) at the start of a fixation interval.

Returns

Float.

4.23 StartNonBlinkEvent Class Reference

This interface is never used as is.

Inherits EyeEvent.

Inherited by FixUpdateEvent, StartFixationEvent, and StartSaccadeEvent.

Public Member Functions

• def getStartGaze (self)
Gaze position at the start of the event (in pixel coordinates set by the screen_pixel_coords command).

• def getStartHREF (self)
HEADREF position at the start of the event.

• def getStartVelocity (self)
Gaze velocity at the start of the event (in visual degrees per second).

• def getStartPPD (self)
Angular resolution at the start of the event (in screen pixels per visual degree, PPD).

© SR Research Ltd. 2003-2024



4.23 StartNonBlinkEvent Class Reference 143

4.23.1 Detailed Description

This interface is never used as is.

FixUpdateEvent, StartFixationEvent and StartSaccadeEvent types inherit this. This also inherits all properties from
EyeEvent.

4.23.2 Member Function Documentation

******** getStartGaze()

def getStartGaze (
self )

Gaze position at the start of the event (in pixel coordinates set by the screen_pixel_coords command).

The first and second items of the tuple store the x- and y- gaze position respectively.

Returns

Two-item tuple in the format of (float, float).

******** getStartHREF()

def getStartHREF (
self )

HEADREF position at the start of the event.

The first and second items of the tuple store the x- and y- HREF data respectively.

Returns

Two-item tuple in the format of (float, float).

******** getStartVelocity()

def getStartVelocity (
self )

Gaze velocity at the start of the event (in visual degrees per second).

Returns

Float.

© SR Research Ltd. 2003-2024



144 Class Documentation

******** getStartPPD()

def getStartPPD (
self )

Angular resolution at the start of the event (in screen pixels per visual degree, PPD).

The first item of the tuple stores the x-coordinate PPD resolution and the second item of the tuple stores the y-
coordinate PPD resolution.

Returns

Two-item tuple in the format of (float, float).

4.24 StartSaccadeEvent Class Reference

Class to represent Start Saccade event.

Inherits StartNonBlinkEvent.

Inherited by EndSaccadeEvent.

Additional Inherited Members

4.24.1 Detailed Description

Class to represent Start Saccade event.

There are no direct properties for this interface. All properties are inherited from StartNonBlinkEvent.

© SR Research Ltd. 2003-2024



Chapter 5

Example implementation of EyeLinkCustomDisplay

#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS “AS
# IS” AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
# TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
# PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR
# CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
# EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
# PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES LOSS OF USE, DATA, OR
# PROFITS OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
# LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
# NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Redistributions in binary form must reproduce the above copyright
# notice, this list of conditions and the following disclaimer in
# the documentation and/or other materials provided with the distribution.
#
# Neither name of SR Research Ltd nor the name of contributors may be used
# to endorse or promote products derived from this software without
# specific prior written permission.
#
# Last updated on 3/18/2021
import pygame
from pygame.locals import *
from math import pi
import array
import pylink
import platform
import sys
import os
#allow to disable sound, or if we failed to initialize pygame.mixer or failed to load audio file
#continue experiment without sound.
DISABLE_AUDIO=False
class PygameEyeLinkCustomDisplay(pylink.EyeLinkCustomDisplay):

def __init__(self, tracker, win):
global DISABLE_AUDIO
pylink.EyeLinkCustomDisplay.__init__(self)
self._disp = win # screen to use for calibration
self._tracker = tracker # connection to the tracker
self._version = ’2021.3.16’
self._last_updated = ’3/16/2021’
pygame.mouse.set_visible(False) # hide mouse cursor
self._bgColor = (128, 128, 128) # target color (foreground)
self._fgColor = (0, 0, 0) # target color (background)
self._targetSize = 32 # diameter of the target
self._targetType = ’circle’ # could be ’circle’ or ’picture’
self._pictureTarget = None # picture target
self._target_beep = None
self._done_beep = None
self._error_beep = None
if not DISABLE_AUDIO:

try:
self._target_beep = pygame.mixer.Sound("type.wav")
self._done_beep = pygame.mixer.Sound("qbeep.wav")
self._error_beep = pygame.mixer.Sound("error.wav")

except Exception as e:
print (’Failed to load audio: ’+ str(e))
#we failed to load audio, so disable it
#if the experiment is run with sudo/root user in Ubuntu, then audio will
#fail. The work around is either allow audio playback permission
#for root user or, run the experiment with non root user.
DISABLE_AUDIO=True

self._size = (384, 320) # size of the camera image



146 Example implementation of EyeLinkCustomDisplay

self._imagebuffer = array.array(’I’) # buffer to store camera image
self._resizedImg = None
self.surf = pygame.display.get_surface()
# image palette; its indices are used to reconstruct the camera image
self._pal = []
# we will use this for text messages
self._fnt = pygame.font.SysFont(’Arial’, 26)
self._w, self._h = self._disp.get_size()
self._cam_region = pygame.Rect((0, 0), (0, 0))
# cache the camera title
self._title = ”
# keep track of mouse states
self.mouse_pos = (self._w/2, self._h/2)
self.last_mouse_state = -1

def __str__(self):
""" overwrite __str__ to show some information about the
CoreGraphicsPsychoPy library
"""
return "Using the CalibrationGraphicsPygame library, " + \

"version %s, " % self._version + \
"last updated on %s" % self._last_updated

def getForegroundColor(self):
""" get the foreground color """
return self._fgColor

def getBackgroundColor(self):
""" get the foreground color """
return self._bgColor

def setCalibrationColors(self, foreground_color, background_color):
""" Set calibration background and foreground colors
Parameters:

foreground_color--foreground color for the calibration target
background_color--calibration background.
"""

self._fgColor = foreground_color
self._bgColor = background_color

def setTargetType(self, type):
""" Set calibration target size in pixels
Parameters:

type: "circle" (default) or "picture"
"""
self._targetType = type

def setTargetSize(self, size):
""" Set calibration target size in pixels"""
self._targetSize = size

def setPictureTarget(self, picture_target):
""" set the movie file to use as the calibration target """
self._pictureTarget = picture_target

def setCalibrationSounds(self, target_beep, done_beep, error_beep):
""" Provide three wav files as the warning beeps
Parameters:

target_beep -- sound to play when the target comes up
done_beep -- calibration is done successfully
error_beep -- calibration/drift-correction error.

"""
# target beep
if target_beep == ”:

self._target_beep = pygame.mixer.Sound("type.wav")
elif target_beep == ’off’:

self._target_beep = None
else:

self._target_beep = pygame.mixer.Sound(target_beep)
# done beep
if done_beep == ”:

self._done_beep = pygame.mixer.Sound("qbeep.wav")
elif done_beep == ’off’:

self._done_beep = None
else:

self._done_beep = pygame.mixer.Sound(done_beep)
# error beep
if error_beep == ”:

self._error_beep = pygame.mixer.Sound("error.wav")
elif error_beep == ’off’:

self._error_beep = None
else:

self._error_beep = pygame.mixer.Sound(error_beep)
def setup_cal_display(self):

""" setup calibration/validation display"""
self.clear_cal_display()

def exit_cal_display(self):
""" exit calibration/validation display"""
self.clear_cal_display()

def record_abort_hide(self):
pass

def clear_cal_display(self):
self._disp.fill(self._bgColor)
pygame.display.flip()
self._disp.fill(self._bgColor)

© SR Research Ltd. 2003-2024



147

def erase_cal_target(self):
self.clear_cal_display()

def draw_cal_target(self, x, y):
""" draw the calibration target, i.e., a bull’s eye"""
if self._targetType == ’picture’:

if self._pictureTarget is None:
print(’ERROR: Provide a picture as the calibration target’)
pygame.quit()
sys.exit()

elif not os.path.exists(self._pictureTarget):
print(’ERROR: Picture %s not found’ % self._pictureTarget)
pygame.quit()
sys.exit()

else:
cal_pic = pygame.image.load(self._pictureTarget)
w, h = cal_pic.get_size()
self._disp.blit(cal_pic, (x - int(w/2.0), y - int(h/2.0)))

else:
pygame.draw.circle(self._disp, self._fgColor, (x, y),

int(self._targetSize / 2.))
pygame.draw.circle(self._disp, self._bgColor, (x, y),

int(self._targetSize / 4.))
pygame.display.flip()

def play_beep(self, beepid):
""" play warning beeps if being requested"""
global DISABLE_AUDIO
# if sound is disabled, don’t play
if DISABLE_AUDIO:

pass
else:

if beepid in [pylink.DC_TARG_BEEP, pylink.CAL_TARG_BEEP]:
if self._target_beep is not None:

self._target_beep.play()
pygame.time.wait(50)

if beepid in [pylink.CAL_ERR_BEEP, pylink.DC_ERR_BEEP]:
if self._error_beep is not None:

self._error_beep.play()
pygame.time.wait(300)

if beepid in [pylink.CAL_GOOD_BEEP, pylink.DC_GOOD_BEEP]:
if self._done_beep is not None:

self._done_beep.play()
pygame.time.wait(100)

def getColorFromIndex(self, colorindex):
""" color scheme for different elements """
if colorindex == pylink.CR_HAIR_COLOR:

return (255, 255, 255, 255)
elif colorindex == pylink.PUPIL_HAIR_COLOR:

return (255, 255, 255, 255)
elif colorindex == pylink.PUPIL_BOX_COLOR:

return (0, 255, 0, 255)
elif colorindex == pylink.SEARCH_LIMIT_BOX_COLOR:

return (255, 0, 0, 255)
elif colorindex == pylink.MOUSE_CURSOR_COLOR:

return (255, 0, 0, 255)
else:

return (0, 0, 0, 0)
def draw_line(self, x1, y1, x2, y2, colorindex):

""" draw lines"""
color = self.getColorFromIndex(colorindex)
# get the camera image rect, then scale
if self._size[0] > 192:

imr = self._img.get_rect()
x1 = int((float(x1) / 192) * imr.w)
x2 = int((float(x2) / 192) * imr.w)
y1 = int((float(y1) / 160) * imr.h)
y2 = int((float(y2) / 160) * imr.h)

# draw the line
if True not in [x < 0 for x in [x1, x2, y1, y2]]:

pygame.draw.line(self._img, color, (x1, y1), (x2, y2))
def draw_lozenge(self, x, y, width, height, colorindex):

""" draw the search limits with two lines and two arcs"""
color = self.getColorFromIndex(colorindex)
if self._size[0] > 192:

imr = self._img.get_rect()
x = int((float(x) / 192) * imr.w)
y = int((float(y) / 160) * imr.h)
width = int((float(width) / 192) * imr.w)
height = int((float(height) / 160) * imr.h)

if width > height:
rad = int(height / 2.)
if rad == 0:

return
else:

pygame.draw.line(self._img,
color,
(x + rad, y),
(x + width - rad, y))

© SR Research Ltd. 2003-2024



148 Example implementation of EyeLinkCustomDisplay

pygame.draw.line(self._img,
color,
(x + rad, y + height),
(x + width - rad, y + height))

pygame.draw.arc(self._img,
color,
[x, y, rad*2, rad*2],
pi/2, pi*3/2, 1)

pygame.draw.arc(self._img,
color,
[x+width-rad*2, y, rad*2, height],
pi*3/2, pi/2 + 2*pi, 1)

else:
rad = int(width / 2.)
if rad == 0:

return
else:

pygame.draw.line(self._img,
color,
(x, y + rad),
(x, y + height - rad))

pygame.draw.line(self._img,
color,
(x + width, y + rad),
(x + width, y + height - rad))

pygame.draw.arc(self._img,
color,
[x, y, rad*2, rad*2],
0, pi, 1)

pygame.draw.arc(self._img,
color,
[x, y+height-rad*2, rad*2, rad*2],
pi, 2*pi, 1)

def get_mouse_state(self):
""" get mouse position and states"""
x, y = pygame.mouse.get_pos()
state = pygame.mouse.get_pressed()
x = x * self._size[0]/self._w/2.0
y = y * self._size[1]/self._h/2.0
return ((x, y), state[0])

def get_input_key(self):
""" handle key input and send it over to the tracker"""
ky = []
for ev in pygame.event.get():

# check keyboard events
if ev.type == KEYDOWN:

keycode = ev.key
if keycode == K_F1:

keycode = pylink.F1_KEY
elif keycode == K_F2:

keycode = pylink.F2_KEY
elif keycode == K_F3:

keycode = pylink.F3_KEY
elif keycode == K_F4:

keycode = pylink.F4_KEY
elif keycode == K_F5:

keycode = pylink.F5_KEY
elif keycode == K_F6:

keycode = pylink.F6_KEY
elif keycode == K_F7:

keycode = pylink.F7_KEY
elif keycode == K_F8:

keycode = pylink.F8_KEY
elif keycode == K_F9:

keycode = pylink.F9_KEY
elif keycode == K_F10:

keycode = pylink.F10_KEY
elif keycode == K_PAGEUP:

keycode = pylink.PAGE_UP
elif keycode == K_PAGEDOWN:

keycode = pylink.PAGE_DOWN
elif keycode == K_UP:

keycode = pylink.CURS_UP
elif keycode == K_DOWN:

keycode = pylink.CURS_DOWN
elif keycode == K_LEFT:

keycode = pylink.CURS_LEFT
elif keycode == K_RIGHT:

keycode = pylink.CURS_RIGHT
elif keycode == K_BACKSPACE:

keycode = ord(’\b’)
elif keycode == K_RETURN:

keycode = pylink.ENTER_KEY
# probe the tracker to see if it’s "simulating gaze
# with mouse". if so, show a warning instead of a blank
# screen to experimenter do so, only when the tracker
# is in Camera Setup screen

© SR Research Ltd. 2003-2024



149

if self._tracker.getCurrentMode() == pylink.IN_SETUP_MODE:
self._tracker.readRequest(’aux_mouse_simulation’)
pylink.pumpDelay(50)
if self._tracker.readReply() == ’1’:

# draw a rectangle to mark the camera image
rec_x = int((self._w - 192*2) / 2.0)
rec_y = int((self._h - 160*2) / 2.0)
rct = pygame.Rect((rec_x, rec_y, 192*2, 160*2))
pygame.draw.rect(self._disp, self._fgColor, rct, 2)
# show some message
msg = ’Simulating gaze with the mouse’
msg_w, msg_h = self._fnt.size(msg)
t_surf = self._fnt.render(msg, True, self._fgColor)
txt_x = int((self._w - msg_w)/2.0)
txt_y = int((self._h - msg_h)/2.0)
self._disp.blit(t_surf, (txt_x, txt_y))
pygame.display.flip()

elif keycode == K_SPACE:
keycode = ord(’ ’)

elif keycode == K_ESCAPE:
keycode = pylink.ESC_KEY

elif keycode == K_TAB:
keycode = ord(’\t’)

elif(keycode == pylink.JUNK_KEY):
keycode = 0

ky.append(pylink.KeyInput(keycode, ev.mod))
return ky

def exit_image_display(self):
""" exit the camera image display"""
self.clear_cal_display()

def alert_printf(self, msg):
print(msg)

def setup_image_display(self, width, height):
""" set up the camera image display
return 1 to request high-resolution camera image"""
self._size = (width, height)
self.clear_cal_display()
self.last_mouse_state = -1
return 1

def image_title(self, text):
""" show the camera image title
target distance, and pupil/CR thresholds below the image. To prevent
drawing glitches, we cache the image title and draw it with the camera
image in the draw_image_line function instead"""
self._title = text

def draw_image_line(self, width, line, totlines, buff):
""" draw the camera image"""
for i in range(width):

try:
self._imagebuffer.append(self._pal[buff[i]])

except:
pass

if line == totlines:
try:

# construct the camera image from the buffer
try:

tmp_buffer = self._imagebuffer.tobytes()
except:

tmp_buffer = self._imagebuffer.tostring()
cam = pygame.image.frombuffer(tmp_buffer,

(width, totlines), ’RGBX’)
self._img = cam
self.draw_cross_hair()
# prepare the camera image
img_w, img_h = (width*2, totlines*2)
self._resizedImg = pygame.transform.scale(cam, (img_w, img_h))
cam_img_pos = ((self._w/2-img_w/2),

(self._h/2-img_h/2))
# prepare the camera image caption
txt_w, txt_h = self._fnt.size(self._title)
txt_surf = self._fnt.render(self._title, True, self._fgColor)
txt_pos = (int(self._w/2 - txt_w/2),

int(self._h/2 + img_h/2 + txt_h/2))
# draw the camera image and the caption
surf = pygame.display.get_surface()
surf.fill(self._bgColor)
surf.blit(self._resizedImg, cam_img_pos)
surf.blit(txt_surf, txt_pos)
pygame.display.flip()

except:
pass

self._imagebuffer = array.array(’I’)
def set_image_palette(self, r, g, b):

""" get the color palette for the camera image"""
self._imagebuffer = array.array(’I’)
sz = len(r)
i = 0

© SR Research Ltd. 2003-2024



150 Example implementation of EyeLinkCustomDisplay

self._pal = []
while i < sz:

rf = int(b[i])
gf = int(g[i])
bf = int(r[i])
self._pal.append((rf « 16) | (gf « 8) | (bf))
i = i + 1

# A short testing script showing the basic usage of this library
# We first instantiate a connection to the tracker (el_tracker), then we open
# a Pygame window (win). We then pass the tracker connection and the Pygame
# window to the graphics environment constructor (CalibrationGraphics).
# The graphics environment, once instantiated, can be configured to customize
# the calibration foreground and background color, the calibration target
# type, the calibration target size, and the beeps we would like to
# play during calibration and validation.
#
# IMPORTANT: Once the graphics environment is properly configured, call the
# pylink.openGraphicsEx() function to request PyLink to use the custom graphics
# environment for calibration instead.
def main():

""" A short script showing how to use this library.
We connect to the tracker, open a Pygame window, and then configure the
graphics environment for calibration. Then, perform a calibration and
disconnect from the tracker.
The doTrackerSetup() command will bring up a gray calibration screen.
When the gray screen comes up, press Enter to show the camera image,
press C to calibrate, V to validate, and O to quit calibration"""
# initialize Pygame
pygame.init()
# get the screen resolution natively supported by the monitor
disp = pylink.getDisplayInformation()
scn_w = disp.width
scn_h = disp.height
# connect to the tracker
el_tracker = pylink.EyeLink("*********")
# open an EDF data file on the Host PC
el_tracker.openDataFile(’test.edf’)
# open a Pygame window
win = pygame.display.set_mode((scn_w, scn_h), FULLSCREEN | DOUBLEBUF)
# send over a command to let the tracker know the correct screen resolution
scn_coords = "screen_pixel_coords = 0 0 %d %d" % (scn_w - 1, scn_h - 1)
el_tracker.sendCommand(scn_coords)
# Instantiate a graphics environment (genv) for calibration
genv = CalibrationGraphics(el_tracker, win)
# Set background and foreground colors for calibration
foreground_color = (0, 0, 0)
background_color = (128, 128, 128)
genv.setCalibrationColors(foreground_color, background_color)
# The calibration target could be a "circle" (default) or a "picture",
genv.setTargetType(’circle’)
# Configure the size of the calibration target (in pixels)
genv.setTargetSize(24)
# Beeps to play during calibration, validation, and drift correction
# parameters: target, good, error
# Each parameter could be ”--default sound, ’off’--no sound, or a wav file
genv.setCalibrationSounds(”, ”, ”)
# Request PyLink to use the graphics environment (genv) we customized above
pylink.openGraphicsEx(genv)
# calibrate the tracker
el_tracker.doTrackerSetup()
# close the data file
el_tracker.closeDataFile()
# disconnect from the tracker
el_tracker.close()
# quit pygame
pygame.quit()
sys.exit()

if __name__ == ’__main__’:
main()

© SR Research Ltd. 2003-2024



Index

__init__ commandResult
EyeLink, 39 EyeLinkCBind, 103
EyeLinkAddress, 60 currentDoubleUsec
EyelinkMessage, 116 EyeLink Utility Functions, 17

__updateimgsize__ currentTime
EyeLinkCustomDisplay, 107 EyeLink Utility Functions, 21

currentUsec
abort EyeLink Utility Functions, 20

EyeLinkCBind, 95
acceptTrigger dataSwitch

EyeLinkCBind, 76 EyeLinkCBind, 79
alert disableAutoCalibration

EyeLink Utility Functions, 20 EyeLink, 43
applyDriftCorrect DisplayInfo, 26

EyeLinkCBind, 98 bits, 27
height, 26

beginRealTimeMode refresh, 27
EyeLink Utility Functions, 21 width, 26

bitmapBackdrop doDriftCorrect
EyeLinkCBind, 74 EyeLinkCBind, 89

bitmapSave doTrackerSetup
EyeLink Utility Functions, 19 EyeLink, 40

bitmapSaveAndBackdrop EyeLinkCBind, 66
EyeLinkCBind, 100 draw_cal_target

bits EyeLinkCustomDisplay, 110
DisplayInfo, 27 draw_cross_hair

breakPressed EyeLinkCustomDisplay, 112
EyeLinkCBind, 90 draw_image_line

broadcastOpen EyeLinkCustomDisplay, 109
EyeLinkCBind, 86 draw_line

ButtonEvent, 25 EyeLinkCustomDisplay, 111
getButtons, 25 draw_lozenge
getStates, 25 EyeLinkCustomDisplay, 112

drawBox
calculateOverallVelocityAndAcceleration EyeLink, 56

EyeLinkCBind, 63 drawCalTarget
calculateVelocity EyeLinkListener, 114

EyeLinkCBind, 72 drawCross
calculateVelocityXY EyeLink, 59

EyeLinkCBind, 70 drawFilledBox
clearScreen EyeLink, 57

EyeLink, 56 drawLine
close EyeLink, 56

EyeLinkCBind, 75 drawText
closeDataFile EyeLink, 55

EyeLinkCBind, 76 dummy_open
closeGraphics EyeLinkCBind, 70

EyeLink Graphics Functions, 16
closeMessageFile echo

EyeLink Utility Functions, 20 EyeLink, 58



152 INDEX

echo_key progressUpdate, 39
EyeLinkCBind, 73 readIOPort, 44

enableAutoCalibration setAccelerationThreshold, 53
EyeLink, 43 setAcceptTargetFixationButton, 41

EndBlinkEvent, 27 setAutoCalibrationPacing, 43
getEndTime, 27 setCalibrationType, 41

EndFixationEvent, 28 setFileEventData, 48
getAverageGaze, 28 setFileEventFilter, 49
getAverageHREF, 28 setFileSampleFilter, 48
getAveragePupilSize, 29 setFixationUpdateAccumulate, 54, 58
getEndPupilSize, 29 setFixationUpdateInterval, 58

EndNonBlinkEvent, 29 setHeuristicFilterOff, 45
getAverageVelocity, 31 setHeuristicFilterOn, 45
getEndGaze, 31 setHeuristicLinkAndFileFilter, 44
getEndHREF, 31 setLinkEventData, 50
getEndPPD, 32 setLinkEventFilter, 51
getEndTime, 30 setLinkSampleFilter, 49
getEndVelocity, 31 setMotionThreshold, 53
getPeakVelocity, 32 setNoRecordEvents, 47

endRealTimeMode setPupilSizeDiameter, 45
EyeLink Utility Functions, 18 setPursuitFixup, 53

EndSaccadeEvent, 32 setRecordingParseType, 55
getAmplitude, 33 setSaccadeVelocityThreshold, 51
getAngle, 33 setSampleSizeForVelAndAcceleration, 40

erase_cal_target setScreenSimulationDistance, 46
EyeLinkCustomDisplay, 109 setSimulationMode, 46

escapePressed setUpdateInterval, 54
EyeLinkCBind, 93 setVelocityAccelerationModel, 40

Event Data Flags, 9 setXGazeConstraint, 42
Event Type Flags, 8 setYGazeConstraint, 42
exit_cal_display writeIOPort, 44

EyeLinkCustomDisplay, 108 EyeLink Graphics Functions, 12
exitCalibration closeGraphics, 16

EyeLinkCBind, 102 getDisplayInformation, 16
eyeAvailable openGraphics, 12

EyeLinkCBind, 69 openGraphicsEx, 12
EyeEvent, 33 setCalibrationColors, 13

getEye, 35 setCalibrationSounds, 14
getRead, 35 setCameraPosition, 15
getStartTime, 36 setDriftCorrectSounds, 15
getTime, 35 setTargetSize, 13
getType, 35 EyeLink Utility Functions, 17

EyeLink, 36 alert, 20
__init__, 39 beginRealTimeMode, 21
clearScreen, 56 bitmapSave, 19
disableAutoCalibration, 43 closeMessageFile, 20
doTrackerSetup, 40 currentDoubleUsec, 17
drawBox, 56 currentTime, 21
drawCross, 59 currentUsec, 20
drawFilledBox, 57 endRealTimeMode, 18
drawLine, 56 flushGetkeyQueue, 18
drawText, 55 getDisplayAPIVersion, 20
echo, 58 getEYELINK, 22
enableAutoCalibration, 43 getLastError, 18
getFixationUpdateAccumulate, 58 inRealTimeMode, 17
getFixationUpdateInterval, 57 msecDelay, 21
markPlayBackStart, 47 openMessageFile, 22
progressSendDataUpdate, 39 pumpDelay, 22

© SR Research Ltd. 2003-2024



INDEX 153

EyeLinkAddress, 59 isRecording, 79
__init__, 60 key_message_pump, 76
getIP, 60 nodeReceive, 87
getPort, 61 nodeRequestTime, 98

EyeLinkCBind, 61 nodeSend, 83
abort, 95 nodeSendMessage, 64
acceptTrigger, 76 open, 86
applyDriftCorrect, 98 openDataFile, 68
bitmapBackdrop, 74 openNode, 102
bitmapSaveAndBackdrop, 100 pollRemotes, 92
breakPressed, 90 pollResponses, 84
broadcastOpen, 86 pollTrackers, 75
calculateOverallVelocityAndAcceleration, 63 pumpMessages, 93
calculateVelocity, 72 quietMode, 82
calculateVelocityXY, 70 readKeyButton, 74
close, 75 readKeyQueue, 67
closeDataFile, 76 readReply, 78
commandResult, 103 readRequest, 80
dataSwitch, 79 readTime, 99
doDriftCorrect, 89 receiveDataFile, 67
doTrackerSetup, 66 requestTime, 71
dummy_open, 70 reset, 68
echo_key, 73 resetData, 91
escapePressed, 93 sendCommand, 68
exitCalibration, 102 sendDataFile, 92
eyeAvailable, 69 sendKeybutton, 81
flushKeybuttons, 103 sendMessage, 73
getButtonStates, 95 sendTimedCommand, 96
getCalibrationMessage, 91 sendTimedCommandEx, 69
getCalibrationResult, 87 setAddress, 99
getCurrentMode, 91 setName, 90
getDataCount, 88 setOfflineMode, 89
getEventDataFlags, 78 startData, 84
getEventTypeFlags, 101 startDriftCorrect, 105
getFloatData, 104 startPlayBack, 65
getImageCrossHairData, 100 startRecording, 77
getkey, 65 startSetup, 81
getkeyEx, 97 stopData, 66
getLastButtonPress, 97 stopPlayBack, 95
getLastButtonStates, 84 stopRecording, 94
getLastData, 88 targetModeDisplay, 87
getLastMessage, 77 terminalBreak, 83
getModeData, 82 trackerTime, 105
getNewestSample, 90 trackerTimeOffset, 101
getNextData, 76 trackerTimeUsec, 78
getNode, 74 trackerTimeUsecOffset, 65
getPositionScalar, 101 userMenuSelection, 70
getRecordingStatus, 86 waitForBlockStart, 72
getSample, 89 waitForData, 85
getSampleDataFlags, 94 waitForModeReady, 102
getTargetPositionAndState, 104 EyeLinkCustomDisplay, 106
getTrackerMode, 80 __updateimgsize__, 107
getTrackerVersion, 64 draw_cal_target, 110
getTrackerVersionString, 93 draw_cross_hair, 112
imageModeDisplay, 71 draw_image_line, 109
inSetup, 66 draw_line, 111
isConnected, 85 draw_lozenge, 112
isInDataBlock, 98 erase_cal_target, 109

© SR Research Ltd. 2003-2024



154 INDEX

exit_cal_display, 108 getCalibrationResult
get_input_key, 111 EyeLinkCBind, 87
get_mouse_state, 112 getCurrentMode
image_title, 109 EyeLinkCBind, 91
play_beep, 110 getData
record_abort_hide, 108 IOEvent, 131
set_image_palette, 109 getDataCount
setup_cal_display, 107 EyeLinkCBind, 88
setup_image_display, 108 getDisplayAPIVersion

EyeLinkListener, 113 EyeLink Utility Functions, 20
drawCalTarget, 114 getDisplayInformation
getTrackerInfo, 114 EyeLink Graphics Functions, 16
imageBackdrop, 115 getEndGaze
sendMessage, 114 EndNonBlinkEvent, 31

EyelinkMessage, 116 getEndHREF
__init__, 116 EndNonBlinkEvent, 31
getText, 117 getEndPPD

EndNonBlinkEvent, 32
FixUpdateEvent, 117 getEndPupilSize

getAverageGaze, 118 EndFixationEvent, 29
getAverageHREF, 118 FixUpdateEvent, 119
getAveragePupilSize, 118 getEndTime
getEndPupilSize, 119 EndBlinkEvent, 27
getStartPupilSize, 118 EndNonBlinkEvent, 30

flushGetkeyQueue getEndVelocity
EyeLink Utility Functions, 18 EndNonBlinkEvent, 31

flushKeybuttons
getEventDataFlags

EyeLinkCBind, 103
EyeLinkCBind, 78

get_input_key ILinkData, 123
EyeLinkCustomDisplay, 111 getEventQueueLength

get_mouse_state ILinkData, 126
EyeLinkCustomDisplay, 112 getEventsInBlock

getAddress ILinkData, 125
ILinkData, 129 getEventTypeFlags

getAmplitude EyeLinkCBind, 101
EndSaccadeEvent, 33 ILinkData, 123

getAngle getEye
EndSaccadeEvent, 33 EyeEvent, 35

getAverageGaze Sample, 138
EndFixationEvent, 28 getEYELINK
FixUpdateEvent, 118 EyeLink Utility Functions, 22

getAverageHREF getFixationUpdateAccumulate
EndFixationEvent, 28 EyeLink, 58
FixUpdateEvent, 118 getFixationUpdateInterval

getAveragePupilSize EyeLink, 57
EndFixationEvent, 29 getFlags
FixUpdateEvent, 118 Sample, 137

getAverageVelocity getFloatData
EndNonBlinkEvent, 31 EyeLinkCBind, 104

getBlockNumber getFreeQueueLength
ILinkData, 125 ILinkData, 127

getButtons getGaze
ButtonEvent, 25 SampleData, 140
Sample, 137 getHeadDistancePrescaler

getButtonStates ILinkData, 122
EyeLinkCBind, 95 getHREF

getCalibrationMessage SampleData, 140
EyeLinkCBind, 91 getImageCrossHairData

© SR Research Ltd. 2003-2024



INDEX 155

EyeLinkCBind, 100 ILinkData, 129
getInput getPacketFlags

Sample, 136 ILinkData, 127
getIP getPeakVelocity

EyeLinkAddress, 60 EndNonBlinkEvent, 32
getkey getPendingCommands

EyeLinkCBind, 65 ILinkData, 128
getkeyEx getPoolResponse

EyeLinkCBind, 97 ILinkData, 128
getLastBufferSize getPort

ILinkData, 124 EyeLinkAddress, 61
getLastBufferType getPositionScalar

ILinkData, 124 EyeLinkCBind, 101
getLastButtonPress getPPD

EyeLinkCBind, 97 Sample, 136
getLastButtonStates getPrescaler

EyeLinkCBind, 84 ILinkData, 122
getLastData getPupilPrescaler

EyeLinkCBind, 88 ILinkData, 122
getLastError getPupilSize

EyeLink Utility Functions, 18 SampleData, 141
getLastItem getQueueSize

ILinkData, 129 ILinkData, 127
getLastItemContent getRawPupil

ILinkData, 125 SampleData, 140
getLastItemStatus getRead

ILinkData, 126 EyeEvent, 35
getLastItemTimeStamp getRecordingStatus

ILinkData, 125 EyeLinkCBind, 86
getLastItemType getReserved

ILinkData, 125 ILinkData, 129
getLastMessage getRightEye

EyeLinkCBind, 77 Sample, 137
getLastPupil getSample

ILinkData, 126 EyeLinkCBind, 89
getLastReceiveTime getSampleDataFlags

ILinkData, 127 EyeLinkCBind, 94
getLastResX ILinkData, 123

ILinkData, 126 getSampleDivisor
getLastResY ILinkData, 122

ILinkData, 126 getSampleQueueLength
getLeftEye ILinkData, 126

Sample, 137 getSampleRate
getLinkFlags ILinkData, 122

ILinkData, 128 getSamplesInBlock
getLostDataTypes ILinkData, 125

ILinkData, 124 getStartGaze
getModeData StartNonBlinkEvent, 143

EyeLinkCBind, 82 getStartHREF
getName StartNonBlinkEvent, 143

ILinkData, 129 getStartPPD
getNewestSample StartNonBlinkEvent, 143

EyeLinkCBind, 90 getStartPupilSize
getNextData FixUpdateEvent, 118

EyeLinkCBind, 76 StartFixationEvent, 142
getNode getStartTime

EyeLinkCBind, 74 EyeEvent, 36
getNodes getStartVelocity

© SR Research Ltd. 2003-2024



156 INDEX

StartNonBlinkEvent, 143 getBlockNumber, 125
getStateFlags getEventDataFlags, 123

ILinkData, 128 getEventQueueLength, 126
getStates getEventsInBlock, 125

ButtonEvent, 25 getEventTypeFlags, 123
getStatus getFreeQueueLength, 127

Sample, 136 getHeadDistancePrescaler, 122
getTargetDistance getLastBufferSize, 124

Sample, 138 getLastBufferType, 124
getTargetFlags getLastItem, 129

Sample, 139 getLastItemContent, 125
getTargetPositionAndState getLastItemStatus, 126

EyeLinkCBind, 104 getLastItemTimeStamp, 125
getTargetX getLastItemType, 125

Sample, 138 getLastPupil, 126
getTargetY getLastReceiveTime, 127

Sample, 138 getLastResX, 126
getText getLastResY, 126

EyelinkMessage, 117 getLinkFlags, 128
MessageEvent, 133 getLostDataTypes, 124

getTime getName, 129
EyeEvent, 35 getNodes, 129
ILinkData, 121 getPacketFlags, 127
IOEvent, 131 getPendingCommands, 128
MessageEvent, 133 getPoolResponse, 128
Sample, 135 getPrescaler, 122

getTrackerAddress getPupilPrescaler, 122
ILinkData, 130 getQueueSize, 127

getTrackerBroadcastAddress getReserved, 129
ILinkData, 130 getSampleDataFlags, 123

getTrackerDataOutputState getSampleDivisor, 122
ILinkData, 128 getSampleQueueLength, 126

getTrackerInfo getSampleRate, 122
EyeLinkListener, 114 getSamplesInBlock, 125

getTrackerMode getStateFlags, 128
EyeLinkCBind, 80 getTime, 121

getTrackerName getTrackerAddress, 130
ILinkData, 129 getTrackerBroadcastAddress, 130

getTrackerVersion getTrackerDataOutputState, 128
EyeLinkCBind, 64

getTrackerName, 129
getTrackerVersionString

getVelocityPrescaler, 122
EyeLinkCBind, 93

haveLeftEye, 123
getType

haveRightEye, 124
EyeEvent, 35

isControlEvent, 124
IOEvent, 131

isEventsEnabled, 127
MessageEvent, 133
Sample, 136 isInBlockWithEvents, 123

getVelocityPrescaler isInBlockWithSamples, 123

ILinkData, 122 isNewBlock, 124
isPoolingRemote, 128

haveLeftEye isSamplesEnabled, 127
ILinkData, 123 image_title

haveRightEye EyeLinkCustomDisplay, 109
ILinkData, 124 imageBackdrop

height EyeLinkListener, 115
DisplayInfo, 26 imageModeDisplay

EyeLinkCBind, 71
ILinkData, 119 inRealTimeMode

getAddress, 129 EyeLink Utility Functions, 17

© SR Research Ltd. 2003-2024



INDEX 157

inSetup openGraphics
EyeLinkCBind, 66 EyeLink Graphics Functions, 12

IOEvent, 130 openGraphicsEx
getData, 131 EyeLink Graphics Functions, 12
getTime, 131 openMessageFile
getType, 131 EyeLink Utility Functions, 22

isBinocular openNode
Sample, 135 EyeLinkCBind, 102

isConnected
EyeLinkCBind, 85 play_beep

isControlEvent EyeLinkCustomDisplay, 110
ILinkData, 124 pollRemotes

isEventsEnabled EyeLinkCBind, 92
ILinkData, 127 pollResponses

isInBlockWithEvents EyeLinkCBind, 84
ILinkData, 123 pollTrackers

isInBlockWithSamples EyeLinkCBind, 75
ILinkData, 123 progressSendDataUpdate

isInDataBlock EyeLink, 39
EyeLinkCBind, 98 progressUpdate

isLeftSample EyeLink, 39
Sample, 135 pumpDelay

isNewBlock EyeLink Utility Functions, 22
ILinkData, 124 pumpMessages

isPoolingRemote EyeLinkCBind, 93
ILinkData, 128

isRecording quietMode
EyeLinkCBind, 79 EyeLinkCBind, 82

isRightSample
Sample, 135 readIOPort

isSamplesEnabled EyeLink, 44
ILinkData, 127 readKeyButton

EyeLinkCBind, 74
key_message_pump readKeyQueue

EyeLinkCBind, 76 EyeLinkCBind, 67
KeyInput, 132 readReply

EyeLinkCBind, 78
markPlayBackStart readRequest

EyeLink, 47 EyeLinkCBind, 80
MessageEvent, 132 readTime

getText, 133 EyeLinkCBind, 99
getTime, 133 receiveDataFile
getType, 133 EyeLinkCBind, 67

msecDelay record_abort_hide
EyeLink Utility Functions, 21 EyeLinkCustomDisplay, 108

refresh
nodeReceive DisplayInfo, 27

EyeLinkCBind, 87 requestTime
nodeRequestTime EyeLinkCBind, 71

EyeLinkCBind, 98 reset
nodeSend EyeLinkCBind, 68

EyeLinkCBind, 83 resetData
nodeSendMessage EyeLinkCBind, 91

EyeLinkCBind, 64
Sample, 133

open getButtons, 137
EyeLinkCBind, 86 getEye, 138

openDataFile getFlags, 137
EyeLinkCBind, 68 getInput, 136

© SR Research Ltd. 2003-2024



158 INDEX

getLeftEye, 137 EyeLink, 54, 58
getPPD, 136 setFixationUpdateInterval
getRightEye, 137 EyeLink, 58
getStatus, 136 setHeuristicFilterOff
getTargetDistance, 138 EyeLink, 45
getTargetFlags, 139 setHeuristicFilterOn
getTargetX, 138 EyeLink, 45
getTargetY, 138 setHeuristicLinkAndFileFilter
getTime, 135 EyeLink, 44
getType, 136 setLinkEventData
isBinocular, 135 EyeLink, 50
isLeftSample, 135 setLinkEventFilter
isRightSample, 135 EyeLink, 51

SampleData, 139 setLinkSampleFilter
getGaze, 140 EyeLink, 49
getHREF, 140 setMotionThreshold
getPupilSize, 141 EyeLink, 53
getRawPupil, 140 setName

sendCommand EyeLinkCBind, 90
EyeLinkCBind, 68 setNoRecordEvents

sendDataFile EyeLink, 47
EyeLinkCBind, 92 setOfflineMode

sendKeybutton EyeLinkCBind, 89
EyeLinkCBind, 81 setPupilSizeDiameter

sendMessage EyeLink, 45
EyeLinkCBind, 73 setPursuitFixup
EyeLinkListener, 114 EyeLink, 53

sendTimedCommand setRecordingParseType
EyeLinkCBind, 96 EyeLink, 55

sendTimedCommandEx setSaccadeVelocityThreshold
EyeLinkCBind, 69 EyeLink, 51

set_image_palette setSampleSizeForVelAndAcceleration
EyeLinkCustomDisplay, 109 EyeLink, 40

setAccelerationThreshold setScreenSimulationDistance
EyeLink, 53 EyeLink, 46

setAcceptTargetFixationButton setSimulationMode
EyeLink, 41 EyeLink, 46

setAddress setTargetSize
EyeLinkCBind, 99 EyeLink Graphics Functions, 13

setAutoCalibrationPacing setup_cal_display
EyeLink, 43 EyeLinkCustomDisplay, 107

setCalibrationColors setup_image_display
EyeLink Graphics Functions, 13 EyeLinkCustomDisplay, 108

setCalibrationSounds setUpdateInterval
EyeLink Graphics Functions, 14 EyeLink, 54

setCalibrationType setVelocityAccelerationModel
EyeLink, 41 EyeLink, 40

setCameraPosition setXGazeConstraint
EyeLink Graphics Functions, 15 EyeLink, 42

setDriftCorrectSounds setYGazeConstraint
EyeLink Graphics Functions, 15 EyeLink, 42

setFileEventData Special Key values for the tracker, 10
EyeLink, 48 StartBlinkEvent, 141

setFileEventFilter startData
EyeLink, 49 EyeLinkCBind, 84

setFileSampleFilter startDriftCorrect
EyeLink, 48 EyeLinkCBind, 105

setFixationUpdateAccumulate StartFixationEvent, 141

© SR Research Ltd. 2003-2024



INDEX 159

getStartPupilSize, 142
StartNonBlinkEvent, 142

getStartGaze, 143
getStartHREF, 143
getStartPPD, 143
getStartVelocity, 143

startPlayBack
EyeLinkCBind, 65

startRecording
EyeLinkCBind, 77

StartSaccadeEvent, 144
startSetup

EyeLinkCBind, 81
stopData

EyeLinkCBind, 66
stopPlayBack

EyeLinkCBind, 95
stopRecording

EyeLinkCBind, 94

targetModeDisplay
EyeLinkCBind, 87

terminalBreak
EyeLinkCBind, 83

Tracker Data Type Constants, 7
Tracker Mode values, 11
trackerTime

EyeLinkCBind, 105
trackerTimeOffset

EyeLinkCBind, 101
trackerTimeUsec

EyeLinkCBind, 78
trackerTimeUsecOffset

EyeLinkCBind, 65

userMenuSelection
EyeLinkCBind, 70

waitForBlockStart
EyeLinkCBind, 72

waitForData
EyeLinkCBind, 85

waitForModeReady
EyeLinkCBind, 102

width
DisplayInfo, 26

writeIOPort
EyeLink, 44

© SR Research Ltd. 2003-2024
# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练程序配置文件
用户可以在此文件中调整实验参数

作者: AI Assistant
日期: 2025-07-09
"""

class ExperimentConfig:
    """实验配置类"""
    
    def __init__(self):
        # ==================== 基本设置 ====================
        self.dummy_mode = False  # 是否使用模拟模式（调试用）
        self.full_screen = True  # 是否全屏显示
        self.eyelink_ip = "*********"  # EyeLink主机IP地址
        
        # ==================== 时间设置 (秒) ====================
        self.instruction_duration = 3.0   # 指示语显示时长
        self.baseline_duration = 7.0       # 基线阶段时长
        self.modulation_duration = 15.0    # 自主调适阶段时长
        self.feedback_duration = 2.0       # 反馈阶段时长
        self.rest_duration = 5.0           # 休息时长

        # 阶段提示语设置
        self.phase_prompt_duration = 2.0   # 阶段提示语显示时长
        self.show_phase_prompts = True     # 是否显示阶段提示语
        
        # ==================== 瞳孔参数 ====================
        self.pupil_min_size = 1.5          # 最小有效瞳孔大小 (mm)
        self.pupil_max_size = 9.0          # 最大有效瞳孔大小 (mm)
        self.max_change_rate = 0.0027      # 最大变化速率 (mm/ms)
        self.feedback_rate = 30            # 反馈更新频率 (Hz)
        self.samples_per_feedback = 33     # 每次反馈的样本数 (1000Hz/30Hz ≈ 33)
        
        # ==================== 实验流程 ====================
        self.trials_per_block = 10         # 每个block的试次数
        self.blocks_per_condition = 3      # 每个条件的block数
        
        # ==================== 显示参数 ====================
        self.baseline_circle_radius = 50   # 基线圆圈半径 (像素)
        self.circle_line_width = 3         # 圆圈线宽
        self.fixation_size = 50            # 注视点大小
        
        # ==================== 颜色设置 ====================
        # 背景颜色：灰色 (150, 150, 150) 转换为PsychoPy范围 (-1到1)
        self.bg_color = [150/255*2-1, 150/255*2-1, 150/255*2-1]
        
        # 注视点颜色：白色
        self.fixation_color = [1, 1, 1]
        
        # 计算绿色亮度值与灰色一致 (Y = 0.2126*R + 0.7152*G + 0.0722*B)
        gray_luminance = 0.2126*150 + 0.7152*150 + 0.0722*150  # 150
        green_g = gray_luminance / 0.7152  # 约209.8
        
        # 基线和反馈圆圈颜色：绿色（亮度与灰色一致）
        self.baseline_color = [0, green_g/255*2-1, 0]
        self.feedback_color = [0, green_g/255*2-1, 0]
        
        # 成功和失败反馈颜色
        self.success_color = [0, green_g/255*2-1, 0]      # 绿色
        self.failure_color = [green_g/255*2-1, 0, 0]      # 红色（亮度与绿色一致）
        
        # ==================== 字体设置 ====================
        self.font_name = 'SimHei'          # 中文字体
        self.font_size = 30                # 字体大小
        self.text_wrap_width = 600         # 文本换行宽度

        # ==================== 阶段提示语文本 ====================
        self.baseline_prompt = "下面将进入基线阶段"
        self.modulation_prompt_enlarge = "下面将进入自主调适阶段\n请尝试放大瞳孔"
        self.modulation_prompt_shrink = "下面将进入自主调适阶段\n请尝试缩小瞳孔"
        self.feedback_prompt = "下面将进入反馈阶段"
        
        # ==================== 数据保存 ====================
        self.results_folder = 'results'    # 结果保存文件夹
        self.save_txt_data = False         # 是否保存额外的TXT数据文件
        
        # ==================== 高级设置 ====================
        self.sampling_rate = 1000          # EyeLink采样率 (Hz)
        self.calibration_type = "HV9"      # 校准类型
        self.validation_enabled = True     # 是否启用验证
        self.drift_check_enabled = False    # 是否启用漂移校正
        
        # 监视器设置
        self.monitor_width = 53.0          # 监视器宽度 (cm)
        self.monitor_distance = 70.0       # 观看距离 (cm)
        
    def validate_config(self):
        """验证配置参数的有效性"""
        errors = []
        
        # 检查时间参数
        if self.instruction_duration <= 0:
            errors.append("指示语时长必须大于0")
        if self.baseline_duration <= 0:
            errors.append("基线阶段时长必须大于0")
        if self.modulation_duration <= 0:
            errors.append("自主调适阶段时长必须大于0")
        if self.feedback_duration <= 0:
            errors.append("反馈阶段时长必须大于0")
        if self.phase_prompt_duration <= 0:
            errors.append("阶段提示语时长必须大于0")
        
        # 检查瞳孔参数
        if self.pupil_min_size >= self.pupil_max_size:
            errors.append("最小瞳孔大小必须小于最大瞳孔大小")
        if self.max_change_rate <= 0:
            errors.append("最大变化速率必须大于0")
        if self.feedback_rate <= 0:
            errors.append("反馈频率必须大于0")
        
        # 检查实验流程参数
        if self.trials_per_block <= 0:
            errors.append("每block试次数必须大于0")
        if self.blocks_per_condition <= 0:
            errors.append("每条件block数必须大于0")
        
        # 检查显示参数
        if self.baseline_circle_radius <= 0:
            errors.append("基线圆圈半径必须大于0")
        if self.circle_line_width <= 0:
            errors.append("圆圈线宽必须大于0")
        
        return errors
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 50)
        print("当前实验配置:")
        print("=" * 50)
        print(f"基本设置:")
        print(f"  模拟模式: {self.dummy_mode}")
        print(f"  全屏显示: {self.full_screen}")
        print(f"  EyeLink IP: {self.eyelink_ip}")
        print()
        print(f"时间设置:")
        print(f"  指示语时长: {self.instruction_duration}秒")
        print(f"  基线阶段时长: {self.baseline_duration}秒")
        print(f"  自主调适阶段时长: {self.modulation_duration}秒")
        print(f"  反馈阶段时长: {self.feedback_duration}秒")
        print(f"  休息时长: {self.rest_duration}秒")
        print(f"  阶段提示语时长: {self.phase_prompt_duration}秒")
        print(f"  显示阶段提示语: {self.show_phase_prompts}")
        print()
        print(f"瞳孔参数:")
        print(f"  有效瞳孔大小范围: {self.pupil_min_size}-{self.pupil_max_size}mm")
        print(f"  最大变化速率: {self.max_change_rate}mm/ms")
        print(f"  反馈频率: {self.feedback_rate}Hz")
        print()
        print(f"实验流程:")
        print(f"  每block试次数: {self.trials_per_block}")
        print(f"  每条件block数: {self.blocks_per_condition}")
        print(f"  总试次数: {self.trials_per_block * self.blocks_per_condition * 2}")
        print("=" * 50)

# 创建默认配置实例
config = ExperimentConfig()

# 如果直接运行此文件，显示配置信息
if __name__ == '__main__':
    config.print_config()
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n配置验证通过!")
